<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getAuditList">
      <template #operation="scope">
        <div v-if="scope.row.status === 1">
          <el-button type="primary" link :icon="Edit" @click="handleAudit(scope.row.id, 'pass')">允许使用</el-button>
          <el-button type="primary" link :icon="Edit" @click="handleAudit(scope.row.id, 'reject')">拒绝使用</el-button>
        </div>
      </template>
    </ProTable>
    <DetailDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="sparePartsUseApproval">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { getAuditList, WarrantyTask, auditPass, auditReject, feedbackTypeOptions } from "@/api/modules/warrantyTask";
import { Edit } from "@element-plus/icons-vue";
import DetailDrawer from "./components/DetailDrawer.vue";
import { useHandleData } from "@/hooks/useHandleData";
import dayjs from "dayjs";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<WarrantyTask.ApprovalRes>[]>([
  {
    prop: "id",
    label: "审批编号",
    width: 150
  },
  {
    prop: "repairFormCode",
    label: "报修单编号",
    width: 150
  },
  // {
  //   prop: "systemType",
  //   label: "报修单类型",
  //   enum: systemTypeOptions,
  //   tag: true
  // },
  {
    prop: "repairFormFeedbackInfo.createByName",
    label: "反馈人员"
  },
  {
    prop: "repairFormFeedbackInfo.createAt",
    label: "反馈时间",
    width: 230,
    render(scope) {
      return dayjs(scope.row.repairFormFeedbackInfo.createAt).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  {
    prop: "repairFormFeedbackInfo.type",
    label: "反馈结果类型",
    width: 160,
    enum: feedbackTypeOptions
  },
  {
    prop: "repairFormFeedbackInfo.remark",
    label: "情况说明",
    width: 180
  },
  {
    prop: "repairFormFeedbackInfo.attachments",
    label: "图片说明",
    width: 200,
    showOverflowTooltip: false,
    render: scope => {
      const imgs = scope.row.repairFormFeedbackInfo.attachments?.split(",") || [];
      return (
        <div>
          {imgs.map((img, index) => (
            <el-image
              class="ml-1"
              initialIndex={index}
              preview-teleported
              src={img + "?x-oss-process=image/resize,w_100,m_lfit"}
              previewSrcList={imgs}
              z-index={99999}
              fit="scale-down"
            />
          ))}
        </div>
      );
    }
  },
  // {
  //   prop: "",
  //   label: "备品备件说明"
  // },
  {
    prop: "partInfo.name",
    label: "备品备件名称"
  },
  {
    prop: "partInfo.quantity",
    label: "备品备件数量"
  },
  {
    prop: "auditRemark",
    label: "审批情况"
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);
/** 审批 */
async function handleAudit(id: string, type: "pass" | "reject") {
  const api = type === "pass" ? auditPass : auditReject;
  await useHandleData(api, { id }, "确认审批");
  await proTable.value?.getTableList();
}
</script>
