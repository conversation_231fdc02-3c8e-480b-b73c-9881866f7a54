import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { PORT1 } from "../config/servicePort";

/**
 * @name 菜单管理模块
 */

// 查询菜单权限树
export const getMenuTree = () => {
  return http.get<Menu.MenuResOptions[]>(PORT1 + `/menu/tree`, {}, { loading: true });
};

// 获取菜单列表（分页）
export const getMenuList = (params: ReqPage & Partial<Menu.MenuSearchParams>, loading = true) => {
  return http.get<ResPage<Menu.MenuResOptions>>(PORT1 + `/menu/paging`, params, { loading });
};

// 获取菜单详情
export const getMenuDetail = (id: string) => {
  return http.get<Menu.MenuResOptions>(PORT1 + `/menu/${id}`);
};

// 新增菜单
export const addMenu = (params: Menu.ReqMenuParams) => {
  return http.post<{ menuId: string }>(PORT1 + `/menu`, params);
};

// 编辑菜单
export const editMenu = (params: Menu.ReqMenuParams) => {
  return http.put(PORT1 + `/menu`, params);
};

// 删除菜单
export const deleteMenu = (ids: string[]) => {
  return http.put(PORT1 + `/menu/delete`, { ids });
};

// 切换菜单状态
export const changeMenuStatus = (params: { id: string; status: number }) => {
  return http.put(PORT1 + `/menu/status`, params);
};

// 获取所有启用的菜单列表（用于下拉选择）
export const getEnabledMenuList = () => {
  return http.get<Menu.MenuResOptions[]>(PORT1 + `/menu/enabled`);
};

// 导出菜单数据
export const exportMenuInfo = (params: Menu.MenuSearchParams) => {
  return http.download(PORT1 + `/menu/export`, params);
};

// 批量导入菜单
export const batchImportMenu = (params: FormData) => {
  return http.post(PORT1 + `/menu/import`, params);
};
