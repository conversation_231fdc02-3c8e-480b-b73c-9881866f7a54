<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增套餐</el-button>
        <!-- <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除
        </el-button> -->
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="View" @click="openDrawer('查看', scope.row)">查看</el-button>
        <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <!-- <el-button type="primary" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button> -->
      </template>
    </ProTable>
    <ServerPackageDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="ServerPackageManage">
import {
  ServerPackage,
  getServerPackageList,
  addServerPackage,
  editServerPackage,
  getServerPackageDetail,
  getServerPackageType,
  upServerPackage,
  downServerPackage,
  hidePackage,
  showPackage
} from "@/api/modules/servicePackage";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { CirclePlus, EditPen, View } from "@element-plus/icons-vue";
import ServerPackageDrawer from "./components/DetailDrawer.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { StatusEnum } from "@/utils/dict";
import { useOrgStore } from "@/stores/modules/orgId";
import { convertProportionToThousandth, priceFormat } from "@/utils";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

/**
 * 查询列表参数
 */
const getTableList = (params: any) => {
  return getServerPackageList(params);
};

/**
 * 显示百分比
 */
const formatPercent = (property: number) => <span>{convertProportionToThousandth(property)}%</span>;

/**
 * 查询套餐类型
 */
const { state: serverType } = useAsyncState(
  getServerPackageType().then(res => Object.entries(res.data).map(([key, value]) => ({ label: value, value: Number(key) }))),
  []
);

const orgStore = useOrgStore();
const orgList = toRef(orgStore, "hospitalList");

const columns = reactive<ColumnProps<ServerPackage.ResServerPackage>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  {
    prop: "id",
    label: "编号",
    width: 200,
    search: { el: "input" }
  },
  {
    prop: "serverType",
    label: "套餐类型",
    width: 180,
    enum: serverType
  },
  {
    prop: "name",
    label: "套餐名称",
    width: 200,
    search: { el: "input" }
  },
  // {
  //   label: "适用院区",
  //   prop: "orgIdList"
  // },
  {
    prop: "price",
    width: 120,
    label: "服务价格",
    render: scope => <span>￥{priceFormat(scope.row.price)}</span>
  },
  {
    prop: "chargingTime",
    label: "计价终止时间",
    render: scope => <span>{scope.row.chargingTime.toString().padStart(2, "0") + ":00"}</span>
  },
  // {
  //   prop: "remark",
  //   label: "详细描述",
  //   width: 120
  // },
  {
    prop: "coverPicture",
    label: "封面图",
    render: scope => {
      return (
        <el-image
          preview-teleported
          src={scope.row.coverPicture + "?x-oss-process=image/resize,w_100,m_lfit"}
          previewSrcList={[scope.row.coverPicture]}
          z-index={99999}
          fit="scale-down"
        />
      );
    }
  },
  {
    prop: "rateCertifiedProperty",
    label: "物业管理费比例",
    width: 120,
    render: scope => {
      return formatPercent(scope.row.rateCertifiedProperty);
    }
  },
  {
    prop: "rateNursing",
    label: "护工费比例",
    render: scope => {
      return formatPercent(scope.row.rateNursing);
    }
  },
  {
    prop: "rateHospital",
    label: "院方管理费比例",
    width: 120,
    render: scope => {
      return formatPercent(scope.row.rateHospital);
    }
  },
  {
    label: "服务套餐状态",
    prop: "status",
    width: 120,
    render: scope => {
      return (
        <el-switch model-value={scope.row.status} active-value={1} inactive-value={0} onChange={() => changeStatus(scope.row)} />
      );
    }
  },
  {
    label: "C端是否显示",
    prop: "display",
    width: 120,
    render: scope => {
      return (
        <el-switch
          model-value={scope.row.display}
          active-value={1}
          inactive-value={0}
          onChange={() => changeDisplay(scope.row)}
        />
      );
    }
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);

/**
 * 打开 drawer(新增、查看、编辑)
 */
const drawerRef = ref<InstanceType<typeof ServerPackageDrawer> | null>(null);
async function openDrawer(title: string, row: Partial<ServerPackage.ServerPackageParams> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: {},
    serverType: unref(serverType),
    orgList: orgList.value.map(item => ({ label: item.name, value: item.id })),
    api: title === "新增" ? addServerPackage : title === "编辑" ? editServerPackage : undefined,
    getTableList: proTable.value?.getTableList
  };
  if (title !== "新增" && row.id) {
    const { data } = await getServerPackageDetail(row.id);
    params.row = {
      ...data,
      billingEndTime: `${row.chargingTime}:00`,
      price: priceFormat(data.price),
      rateCertifiedProperty: convertProportionToThousandth(data.rateCertifiedProperty),
      rateHospital: convertProportionToThousandth(data.rateHospital),
      rateNursing: convertProportionToThousandth(data.rateNursing)
    };
  }
  drawerRef.value?.acceptParams(params);
}

/**
 * 删除套餐信息
 */
// const handleDelete = async (params: ServerPackage.ResServerPackage) => {
//   await useHandleData(deleteServerPackage, [params.id], `删除【${params.name}】套餐`);
//   proTable.value?.getTableList();
// };

/**
 * 批量删除套餐信息
 */
// async function batchDelete(selectedListIds: string[]) {
//   await useHandleData(deleteServerPackage, selectedListIds, "删除所选套餐信息");
//   proTable.value?.getTableList();
// }

/**
 * 禁用启用套餐
 * @param row
 */
async function changeStatus(row: ServerPackage.ResServerPackage) {
  if (row.status === StatusEnum.NO) {
    await useHandleData(downServerPackage, row.id, `禁用【${row.name}】套餐`);
  } else {
    await useHandleData(upServerPackage, row.id, `启用【${row.name}】套餐`);
  }

  proTable.value?.getTableList();
}

/***
 * 显示隐藏套餐
 */
async function changeDisplay(row: ServerPackage.ResServerPackage) {
  if (row.display === 1) {
    await useHandleData(hidePackage, row.id, `隐藏【${row.name}】套餐`);
  } else {
    await useHandleData(showPackage, row.id, `显示【${row.name}】套餐`);
  }

  proTable.value?.getTableList();
}
</script>
