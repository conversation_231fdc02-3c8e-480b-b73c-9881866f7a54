<template>
  <div class="table-box">
    <!-- 备品备件数量明细 -->
    <ProTable ref="proTable" :columns="columns" :request-api="requestApi" :search-col="{ xs: 1, sm: 2, md: 2, lg: 4, xl: 5 }">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="Download" @click="handleExport">导出</el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="dailyProfitSharing">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import {
  exportQuantityDetail,
  getQuantityDetail,
  InvestorTypeOptions,
  quantityStatusOptions,
  SpareParts
} from "@/api/modules/spareParts";
import { ReqPage } from "@/api/interface";
import { priceFormat } from "@/utils";
import dayjs from "dayjs";
import { useDownload } from "@/hooks/useDownload";
import { Download } from "@element-plus/icons-vue";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();
const route = useRoute();

function requestApi(params: ReqPage & { code: string }) {
  return getQuantityDetail(route.params.id as string, params);
}

const columns = reactive<ColumnProps<SpareParts.QuantityRes>[]>([
  {
    prop: "code",
    label: "备品备件编号",
    width: 200
  },
  {
    prop: "batchCode",
    label: "批次编号",
    width: 200,
    search: { el: "input" }
  },
  {
    prop: "name",
    label: "备品备件名称"
  },
  {
    prop: "price",
    label: "单价",
    render: ({ row }) => {
      return <span>{priceFormat(row.price)} 元</span>;
    }
  },
  {
    prop: "status",
    label: "备品备件状态",
    search: { el: "select" },
    enum: quantityStatusOptions
  },
  {
    prop: "outTime",
    label: "出库时间",
    render: scope => <span>{scope.row.outTime ? dayjs(scope.row.outTime).format("YYYY-MM-DD HH:mm:ss") : "--"}</span>,
    search: { el: "date-picker", props: { type: "daterange", valueFormat: "YYYYMMDD" } }
  },
  {
    prop: "entryTime",
    label: "入库时间",
    render: scope => <span>{scope.row.entryTime ? dayjs(scope.row.entryTime).format("YYYY-MM-DD HH:mm:ss") : "--"}</span>
  },
  {
    prop: "applyId",
    label: "关联单据编号",
    render: ({ row }) => {
      return <span>{row.stockApplyId || row.sparePartApplyId}</span>;
    }
  },
  {
    prop: "investorType",
    label: "出资方类型",
    enum: InvestorTypeOptions
  },
  {
    label: "出资方名称",
    prop: "investor"
  }
]);

function handleExport() {
  const searchParam = proTable.value?.searchParam;
  useDownload(
    params => exportQuantityDetail(route.params.id as string, params),
    `备件数量明细导出-${dayjs().format("YYYYMMDD")}`,
    searchParam
  );
}
</script>
