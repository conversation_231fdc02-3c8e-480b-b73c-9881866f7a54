<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="ordreHistory"></ProTable>
  </div>
</template>

<script setup lang="tsx" name="modifyHistory">
import ProTable from "@/components/ProTable/index.vue";
import { CompanionshipOrder } from "@/api/modules/companionshipOrder";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { ordreHistory } from "@/api/modules/companionshipOrder";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<CompanionshipOrder.OrderHistory>[]>([
  {
    prop: "orderId",
    label: "陪护单号",
    width: 180,
    search: { el: "input" }
  },
  // {
  //   prop: "",
  //   label: "陪护类型"
  // },
  {
    prop: "eventName",
    label: "事件名"
  },
  {
    prop: "before",
    label: "修改前内容",
    width: 260,
    showOverflowTooltip: false
  },
  {
    prop: "after",
    label: "修改后内容",
    width: 260,
    showOverflowTooltip: false
  },
  {
    prop: "operatorMobile",
    label: "修改人手机号"
  },
  {
    prop: "operatorName",
    label: "修改人"
  },
  {
    prop: "time",
    label: "修改时间"
  }
]);
</script>

<style scoped>
.table-box ::v-deep .el-table .cell {
  white-space: pre-line;
}
</style>
