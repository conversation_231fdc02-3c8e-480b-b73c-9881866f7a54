<template>
  <el-dialog title="请选择护工" v-model="dialogVisible">
    <el-form ref="ruleFormRef" label-width="100px" label-suffix=" :" @submit="onSubmit">
      <el-form-item :error="errors.nursing" label="护工" required>
        <el-select
          class="w-full"
          v-bind="formData.nursing"
          filterable
          remote
          value-key="value"
          reserve-keyword
          :remote-method="remoteMethod"
          :loading="isLoading"
        >
          <el-option v-for="item in options" :key="item.value" :value="item" :label="item.label" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="onSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { toTypedSchema } from "@vee-validate/yup";
import { object } from "yup";
import { useForm } from "vee-validate";
import { Nurse, getNurseList, setNurseSchedule } from "@/api/modules/nurseManage";

const dialogVisible = ref(false);
const dialogProps = ref<Nurse.ResNurseSchedule>();

interface SelectOption {
  value: string;
  label: string;
}

const {
  state: options,
  isLoading,
  execute: refreshoptions
} = useAsyncState<SelectOption[], [string?]>(
  async query => {
    const { data } = await getNurseList({ name: query, pageIndex: 1, pageSize: 10 }, false);
    return data.records.map((item: any) => ({ value: item.id, label: item.name }));
  },
  [],
  { immediate: false }
);

const remoteMethod = (query?: string) => {
  refreshoptions(300, query);
};

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    nursing: object().required().label("护工")
  })
);

const { defineComponentBinds, handleSubmit, errors } = useForm<{
  nursing: { value: string; label: string };
}>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  nursing: defineComponentBinds("nursing", elPlusConfig)
});

let getTableDate: any = null;
// 接收父组件传过来的参数
const acceptParams = (params: Nurse.ResNurseSchedule, getTableList: any) => {
  refreshoptions();
  dialogVisible.value = true;
  dialogProps.value = params;
  getTableDate = getTableList;
};

const onSubmit = handleSubmit(async values => {
  await setNurseSchedule({
    id: dialogProps.value!.id,
    nursingId: values.nursing.value,
    nursingName: values.nursing.label
  });
  ElMessage.success("操作成功");
  getTableDate();
  dialogVisible.value = false;
});

defineExpose({
  acceptParams
});
</script>
