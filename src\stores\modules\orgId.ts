import { getDepartmentList, getOrganizationList, getOrganizationTree } from "@/api/modules/organization";
import { defineStore } from "pinia";

export const useOrgStore = defineStore("useOrgStore", () => {
  /** 组织机构 */
  const { state: orgTree, execute: refreshOrgTree } = useAsyncState(
    async () => {
      const { data } = await getOrganizationTree();
      return data;
    },
    [],
    { immediate: false }
  );
  /** 医院 */
  const { state: hospitalList, execute: refreshHospitalList } = useAsyncState(async () => {
    const { data } = await getOrganizationList();
    return data;
  }, []);

  /** 物业 */
  const { state: departmentList, execute: refreshPropertyList } = useAsyncState(async () => {
    const { data } = await getDepartmentList();
    return data;
  }, []);

  const refreshOrg = () => Promise.all([refreshHospitalList(), refreshPropertyList()]);

  return { hospitalList, refreshOrg, orgTree, refreshOrgTree, departmentList };
});
