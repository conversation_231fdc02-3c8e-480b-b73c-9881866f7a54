<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getBillDetailApi">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="Download" @click="handleExport">导出</el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="dailyProfitSharing">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { Bill, getBillDetailApi, consumerLogVersion, exportBillDetail } from "@/api/modules/billing";
import { convertProportionToThousandth, fuckDateFormat, priceFormat } from "@/utils";
import { Download } from "@element-plus/icons-vue";
import { useTabsStore } from "@/stores/modules/tabs";
import { useDownload } from "@/hooks/useDownload";
import dayjs from "dayjs";
import { ElTag } from "element-plus";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();
const route = useRoute();
const tabStore = useTabsStore();

const defaultOrderId = route.query.orderId && typeof route.query.orderId === "string" ? route.query.orderId : "";
if (defaultOrderId) {
  tabStore.setTabsTitle(`费用明细-${defaultOrderId}`);
}

const columns = reactive<ColumnProps<Bill.ConsumerLogProperties>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  {
    prop: "orderId",
    label: "陪护单号",
    search: { el: "input", defaultValue: defaultOrderId },
    width: 180
  },
  // {
  //   prop: "id",
  //   label: "账单编号",
  //   width: 180
  // },
  {
    prop: "itemName",
    label: "服务名称"
  },
  {
    prop: "date",
    label: "产生费用时间",
    width: 160,
    search: { el: "date-picker", props: { type: "daterange", valueFormat: "YYYYMMDD" } },
    render: ({ row }) => {
      return fuckDateFormat(row.date);
    }
  },
  {
    prop: "totalPrice",
    label: "产生费用金额",
    render: ({ row }) => {
      return `${priceFormat(row.totalPrice)}元`;
    }
  },
  {
    prop: "patientName",
    label: "病人姓名"
  },
  {
    prop: "nursingNames",
    label: "护工姓名",
    width: 160,
    render: ({ row }) => {
      return (
        <div>
          {row.nursingList.map(({ nursingName }) => (
            <ElTag class="mr-1">{nursingName}</ElTag>
          ))}
        </div>
      );
    }
  },
  {
    prop: "rateCertifiedProperty",
    label: "物业管理分成",
    render: ({ row }) => {
      return `${priceFormat(row.priceCertifiedProperty)}(${convertProportionToThousandth(row.rateCertifiedProperty)}%)`;
    }
  },
  {
    prop: "rateNursing",
    label: "护工费总分成",
    render: ({ row }) => {
      return `${priceFormat(row.priceNursing)}(${convertProportionToThousandth(row.rateNursing)}%)`;
    }
  },
  {
    prop: "rateHospital",
    label: "院方分成",
    render: ({ row }) => {
      return `${priceFormat(row.priceHospital)}(${convertProportionToThousandth(row.rateHospital)}%)`;
    }
  },
  {
    prop: "version",
    label: "费用明细状态",
    tag: true,
    enum: consumerLogVersion,
    search: { el: "select" },
    render: ({ row }) => {
      return row.version === "1" ? "有效" : "无效";
    }
  }
]);

function handleExport() {
  const searchParam = proTable.value?.searchParam;
  useDownload(exportBillDetail, `每日分成导出-${dayjs().format("YYYYMMDD")}`, searchParam);
}
</script>
