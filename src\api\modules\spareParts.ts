import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";

export namespace SpareParts {
  export enum PartsStatusEnum {
    Pending = 0,
    Stocked = 1
  }

  export enum ApprovalStatusEnum {
    Passed = 1,
    Rejected = 2,
    Pending = 0
  }

  export interface PartsRes {
    createBy: number;
    updateBy: number;
    createdAt: number;
    updatedAt: number;
    /**
     * id
     */
    id: string;
    /**
     * 编码
     */
    code: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 备注
     */
    remark: string;
    /**
     * 数量
     */
    quantity: number;
    /**
     * 可用数量
     */
    balance: number;
    /**
     * 单价（分）
     */
    price: number;
    /**
     * 单位
     */
    unit: string;
    /**
     * 状态（0-待入库，1-已入库）
     */
    status: PartsStatusEnum;
    entryTime: number;
  }

  export interface PartParams {
    /**
     * id
     */
    id?: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 备注
     */
    remark: string;
    /**
     * 数量
     */
    quantity: number;
    /**
     * 单价（分）
     */
    price: number;
    /**
     * 单位
     */
    unit: string;
  }

  export enum QuantityStatus {
    Pending = 0,
    Available = 1,
    Used = 2
  }

  export interface QuantityRes {
    createBy: number;
    updateBy: number;
    createdAt: number;
    updatedAt: number;
    deleted: number;
    id: string;
    batchCode: string;
    code: string;
    name: string;
    price: number;
    sparePartBatchId: string;
    /* 状态（0-待入库，1-可用，2-已使用）*/
    status: QuantityStatus;
    stockApplyId: string;
    entryTime: string;
    sparePartApplyId: string;
    outTime: string;
  }

  interface PartBatch {
    /**
     * 数量
     */
    quantity: number;
    /**
     * 备件批次id
     */
    partBatchId?: string;
  }

  export enum InvestorType {
    /** 物业 */
    Property = 1,
    /** 医院 */
    Hospital = 2
  }

  /**
   * 审批单提交
   */
  export interface ApprovalParams {
    /**
     * 出资方类型（1-物业，2-医院）
     */
    investorType?: InvestorType;
    /**
     * 出资方id
     */
    orgId?: string;
    /**
     * 出资方名称
     */
    investor?: string;
    /**
     * 入库时间（时间戳ms）
     */
    entryTime?: number;
    /**
     * 附件数组
     */
    attachments?: {
      name: string;
      url: string;
    }[];
    /**
     * 说明
     */
    remark?: string;
    /**
     * 备件批次数组
     */
    partBatchs?: PartBatch[];
  }

  /**
   * 审批单返回
   */
  export interface ApprovalRes extends ApprovalParams {
    id: string;
    version: string;
    createdAt: number;
    createBy: string;
    updatedAt: number;
    status: ApprovalStatusEnum;
  }
}

export const partsStatusOptions = [
  { value: SpareParts.PartsStatusEnum.Pending, label: "待入库" },
  { value: SpareParts.PartsStatusEnum.Stocked, label: "已入库" }
];

export const quantityStatusOptions = [
  { value: SpareParts.QuantityStatus.Pending, label: "待入库" },
  { value: SpareParts.QuantityStatus.Available, label: "可用" },
  { value: SpareParts.QuantityStatus.Used, label: "已使用" }
];

export const InvestorTypeOptions = [
  { value: SpareParts.InvestorType.Property, label: "物业" },
  { value: SpareParts.InvestorType.Hospital, label: "医院" }
];

export const ApprovalStatusOptions = [
  { value: SpareParts.ApprovalStatusEnum.Passed, label: "通过" },
  { value: SpareParts.ApprovalStatusEnum.Rejected, label: "驳回" },
  { value: SpareParts.ApprovalStatusEnum.Pending, label: "待审批" }
];

/**
 * @name 备品备件管理列表
 */
export const getSparePartsBatch = (params: ReqPage & { name?: string }, loading: Boolean = true) => {
  return http.get<ResPage<SpareParts.PartsRes>>(PORT1 + `/project/part/paging`, params, { loading });
};

/**
 * @name 备品备件详情
 */
export const getSparePartsDetail = (id: string, loading: Boolean = false) => {
  return http.get<SpareParts.PartsRes>(PORT1 + `/project/part/detail/${id}`, {}, { loading });
};

/**
 * @name 备品备件新增
 */
export const addSpareParts = (params: Partial<SpareParts.PartParams>, loading: Boolean = false) => {
  return http.post(PORT1 + `/project/part/create`, params, { loading });
};

/**
 * @name 备品备件编辑
 */
export const editSpareParts = (params: Partial<SpareParts.PartParams>, loading: Boolean = false) => {
  return http.post(PORT1 + `/project/part/update`, params, { loading });
};

/**
 * @name 数量明细列表
 */
export const getQuantityDetail = (
  id: string,
  params: ReqPage & { code: string; outTime?: [number, number] },
  loading: Boolean = true
) => {
  const { outTime, ...rest } = params;
  if (outTime && outTime.length) {
    (rest as any).outTime = `${outTime[0]},${outTime[1]}`;
  }
  return http.get<ResPage<SpareParts.QuantityRes>>(PORT1 + `/project/part/detail/paging/${id}`, rest, { loading });
};

/**
 * @name 数量明细导出
 */
export const exportQuantityDetail = (id: string, params: { code: string }) => {
  return http.download(PORT1 + `/project/part/detail/export/${id}`, params);
};

/**
 * @name 审批单列表
 */
export const getApprovalList = (params: ReqPage, loading: Boolean = true) => {
  return http.get<ResPage<SpareParts.ApprovalRes>>(PORT1 + `/project/part/stock/paging`, params, { loading });
};

/**
 * @name 创建审批单
 */
export const createApproval = (params: SpareParts.ApprovalParams, loading: Boolean = false) => {
  return http.post(PORT1 + `/project/part/stock/create`, params, { loading });
};

/**
 * @name 审批单详情
 */
export const getApprovalDetail = (id: string) => {
  return http.get<SpareParts.ApprovalRes>(PORT1 + `/project/part/stock/detail/${id}`, undefined, { loading: true });
};

/**
 * @name 审批单编辑
 */
export const updateApproval = (params: SpareParts.ApprovalParams & { id: string }) => {
  return http.post(PORT1 + `/project/part/stock/update`, params, { loading: true });
};

/**
 * @name 审批单审核
 */
export const auditApproval = (params: { id: string; status: SpareParts.ApprovalStatusEnum; remark?: string }) => {
  return http.post(PORT1 + `/project/part/stock/audit`, params, { loading: true });
};
