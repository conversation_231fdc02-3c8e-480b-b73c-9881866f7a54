<template>
  <el-dialog title="请选择付费方式" v-model="dialogVisible">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit">
      <el-form-item :error="errors.billType" label="付费方式" required>
        <el-select v-bind="formData.billType">
          <el-option v-for="item in billTypeOptions" :key="item.value" v-bind="item">{{ item.label }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :error="errors.price"
        label="付费金额"
        required
        v-show="!formData.billType.modelValue || formData.billType.modelValue === Bill.BillType.Prepayment"
      >
        <el-input v-bind="formData.price" />
      </el-form-item>
      <el-form-item
        :error="errors.billSubType"
        label="付费周期"
        required
        v-show="!formData.billType.modelValue || formData.billType.modelValue === Bill.BillType.PeriodicBill"
      >
        <el-select v-bind="formData.billSubType">
          <el-option v-for="item in billSubTypeOptions" :key="item.value" v-bind="item">{{ item.label }}</el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useCompanionshipOrderStore, DialogType } from "@/stores/modules/companionshipOrder";
import { CompanionshipOrder, updateBill } from "@/api/modules/companionshipOrder";
import { billTypeOptions, billSubTypeOptions } from "@/api/modules/billing";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string, number } from "yup";
import { useForm } from "vee-validate";
import { Bill } from "@/api/modules/billing";
import { priceFormat, priceToCent } from "@/utils";

const companionshipOrderStore = useCompanionshipOrderStore();

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    billType: number().required().label("付费方式"),
    price: string()
      .matches(/^(?:\d+(\.\d{1,2})?)?$/, "价格必须大于零，且最多保留两位小数")
      .when("billType", ([billType], schema) => {
        return billType !== Bill.BillType.Prepayment ? schema.nullable() : schema.required("请输入预付金额").label("预付金额");
      }),
    billSubType: number().when("billType", ([billType], schema) => {
      return billType !== Bill.BillType.PeriodicBill ? schema : schema.required("请选择付费周期").label("付费周期");
    })
  })
);

const { defineComponentBinds, handleSubmit, resetForm, errors, setValues } = useForm<{
  billType: Bill.BillType;
  billSubType?: Bill.BillSubType;
  price?: string;
}>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  billType: defineComponentBinds("billType", elPlusConfig),
  billSubType: defineComponentBinds("billSubType", elPlusConfig),
  price: defineComponentBinds("price", elPlusConfig)
});

let orderId: string;
const DIALOGTYPE = DialogType.PayType;
const dialogVisible = computed<boolean>({
  set(val) {
    if (val) {
      companionshipOrderStore.dialogInfo.dialogVisible = DIALOGTYPE;
    } else {
      companionshipOrderStore.dialogInfo.dialogVisible = DialogType.None;
    }
    resetForm();
  },
  get() {
    if (companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE) {
      const price = companionshipOrderStore.dialogInfo.info!.bill.price;
      orderId = companionshipOrderStore.dialogInfo.info!.base.id;
      setValues({
        billType:
          companionshipOrderStore.dialogInfo.info!.bill.billType == -1
            ? undefined
            : companionshipOrderStore.dialogInfo.info!.bill.billType,
        billSubType: companionshipOrderStore.dialogInfo.info!.bill.billSubType
          ? companionshipOrderStore.dialogInfo.info!.bill.billSubType
          : undefined,
        price: priceFormat(price).toString()
      });
    }
    return companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE;
  }
});

// TODO ID 提交
const onSubmit = handleSubmit(async values => {
  console.log(values);
  const params: CompanionshipOrder.BillParams = {
    orderId: orderId,
    billType: values.billType,
    billSubType: values.billSubType,
    price: priceToCent(values.price)
  };
  if (params.billType === Bill.BillType.PeriodicBill) {
    delete params.price;
  } else {
    delete params.billSubType;
  }

  await updateBill(params);
  ElMessage("修改成功");
  companionshipOrderStore.dialogInfo = {
    info: undefined,
    dialogVisible: DialogType.None
  };
  companionshipOrderStore.getTableList!();
});
</script>
