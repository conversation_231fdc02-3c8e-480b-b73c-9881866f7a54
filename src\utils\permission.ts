import { useAuthStore } from "@/stores/modules/auth";
import { useUserStore } from "@/stores/modules/user";

/**
 * 权限控制工具类
 */
export class PermissionUtils {
  /**
   * 检查用户是否有访问指定菜单的权限
   */
  static hasMenuPermission(menuKey: string): boolean {
    const authStore = useAuthStore();
    const userStore = useUserStore();

    if (!userStore.token) {
      return false;
    }

    const userMenus = authStore.authMenuList;
    return this.findMenuPermission(userMenus, menuKey);
  }

  /**
   * 检查用户是否有指定按钮的权限
   */
  static hasButtonPermission(buttonKey: string): boolean {
    const authStore = useAuthStore();
    const userStore = useUserStore();

    if (!userStore.token) {
      return false;
    }

    const userButtons = authStore.authButtonList;
    return userButtons.includes(buttonKey);
  }

  /**
   * 检查路由是否需要权限验证
   */
  static requiresAuth(route: any): boolean {
    if (route.meta && typeof route.meta.requiresAuth === "boolean") {
      return route.meta.requiresAuth;
    }
    return true;
  }

  /**
   * 递归查找菜单权限
   */
  private static findMenuPermission(menus: Menu.MenuResOptions[], menuKey: string): boolean {
    for (const menu of menus) {
      if (menu.menuKey === menuKey && menu.status === 1) {
        return true;
      }
      if (menu.childrenList && menu.childrenList.length > 0) {
        if (this.findMenuPermission(menu.childrenList, menuKey)) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * 检查用户是否为超级管理员
   */
  static isSuperAdmin(): boolean {
    const userStore = useUserStore();
    return userStore.userInfo?.type === 1;
  }
}

/**
 * Vue 3 组合式 API 权限检查 Hook
 */
export function usePermission() {
  const hasMenuPermission = (menuKey: string): boolean => {
    return PermissionUtils.hasMenuPermission(menuKey);
  };

  const hasButtonPermission = (buttonKey: string): boolean => {
    return PermissionUtils.hasButtonPermission(buttonKey);
  };

  const isSuperAdmin = (): boolean => {
    return PermissionUtils.isSuperAdmin();
  };

  return {
    hasMenuPermission,
    hasButtonPermission,
    isSuperAdmin
  };
}

/**
 * 权限指令
 */
export const permissionDirective = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding;
    if (value) {
      const hasPermission = PermissionUtils.hasMenuPermission(value) || PermissionUtils.isSuperAdmin();
      if (!hasPermission) {
        el.style.display = "none";
      }
    }
  },

  updated(el: HTMLElement, binding: any) {
    const { value } = binding;
    if (value) {
      const hasPermission = PermissionUtils.hasMenuPermission(value) || PermissionUtils.isSuperAdmin();
      el.style.display = hasPermission ? "" : "none";
    }
  }
};
