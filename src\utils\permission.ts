import { useAuthStore } from "@/stores/modules/auth";
import { useUserStore } from "@/stores/modules/user";

/**
 * 权限控制工具类
 */
export class PermissionUtils {
  /**
   * 检查用户是否有访问指定菜单的权限
   * @param menuKey 菜单权限标识
   * @returns 是否有权限
   */
  static hasMenuPermission(menuKey: string): boolean {
    const authStore = useAuthStore();
    const userStore = useUserStore();

    // 如果用户未登录，返回 false
    if (!userStore.token) {
      return false;
    }

    // 获取用户的菜单权限列表
    const userMenus = authStore.authMenuList;
    
    // 递归查找菜单权限
    return this.findMenuPermission(userMenus, menuKey);
  }

  /**
   * 检查用户是否有指定按钮的权限
   * @param buttonKey 按钮权限标识
   * @returns 是否有权限
   */
  static hasButtonPermission(buttonKey: string): boolean {
    const authStore = useAuthStore();
    const userStore = useUserStore();

    // 如果用户未登录，返回 false
    if (!userStore.token) {
      return false;
    }

    // 获取用户的按钮权限列表
    const userButtons = authStore.authButtonList;
    
    // 检查按钮权限
    return userButtons.includes(buttonKey);
  }

  /**
   * 检查路由是否需要权限验证
   * @param route 路由信息
   * @returns 是否需要权限验证
   */
  static requiresAuth(route: any): boolean {
    // 检查路由 meta 中的 requiresAuth 字段
    if (route.meta && typeof route.meta.requiresAuth === 'boolean') {
      return route.meta.requiresAuth;
    }

    // 默认需要权限验证
    return true;
  }

  /**
   * 递归查找菜单权限
   * @param menus 菜单列表
   * @param menuKey 菜单权限标识
   * @returns 是否找到权限
   */
  private static findMenuPermission(menus: Menu.MenuResOptions[], menuKey: string): boolean {
    for (const menu of menus) {
      // 检查当前菜单
      if (menu.menuKey === menuKey && menu.status === 1) {
        return true;
      }

      // 递归检查子菜单
      if (menu.childrenList && menu.childrenList.length > 0) {
        if (this.findMenuPermission(menu.childrenList, menuKey)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 过滤用户有权限的菜单
   * @param menus 原始菜单列表
   * @returns 过滤后的菜单列表
   */
  static filterMenusByPermission(menus: Menu.MenuResOptions[]): Menu.MenuResOptions[] {
    const userStore = useUserStore();

    // 如果用户未登录，返回空数组
    if (!userStore.token) {
      return [];
    }

    return menus.filter(menu => {
      // 如果菜单不需要权限验证，直接显示
      if (!menu.requiresAuth) {
        return true;
      }

      // 检查用户是否有该菜单权限
      if (this.hasMenuPermission(menu.menuKey)) {
        // 递归过滤子菜单
        if (menu.childrenList && menu.childrenList.length > 0) {
          menu.childrenList = this.filterMenusByPermission(menu.childrenList);
        }
        return true;
      }

      return false;
    });
  }

  /**
   * 获取用户的所有权限标识
   * @returns 权限标识数组
   */
  static getUserPermissions(): string[] {
    const authStore = useAuthStore();
    const permissions: string[] = [];

    // 递归收集菜单权限
    const collectPermissions = (menus: Menu.MenuResOptions[]) => {
      menus.forEach(menu => {
        if (menu.menuKey && menu.status === 1) {
          permissions.push(menu.menuKey);
        }
        if (menu.childrenList && menu.childrenList.length > 0) {
          collectPermissions(menu.childrenList);
        }
      });
    };

    collectPermissions(authStore.authMenuList);

    // 添加按钮权限
    permissions.push(...authStore.authButtonList);

    return permissions;
  }

  /**
   * 检查用户是否为超级管理员
   * @returns 是否为超级管理员
   */
  static isSuperAdmin(): boolean {
    const userStore = useUserStore();
    
    // 检查用户角色或特定标识
    return userStore.userInfo?.type === 1; // 假设 type=1 表示超级管理员
  }

  /**
   * 权限验证装饰器（用于组件方法）
   * @param permission 权限标识
   * @returns 装饰器函数
   */
  static requirePermission(permission: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value;

      descriptor.value = function (...args: any[]) {
        if (PermissionUtils.hasMenuPermission(permission) || PermissionUtils.isSuperAdmin()) {
          return originalMethod.apply(this, args);
        } else {
          ElMessage.warning('您没有执行此操作的权限');
          return;
        }
      };

      return descriptor;
    };
  }
}

/**
 * Vue 3 组合式 API 权限检查 Hook
 */
export function usePermission() {
  /**
   * 检查菜单权限
   */
  const hasMenuPermission = (menuKey: string): boolean => {
    return PermissionUtils.hasMenuPermission(menuKey);
  };

  /**
   * 检查按钮权限
   */
  const hasButtonPermission = (buttonKey: string): boolean => {
    return PermissionUtils.hasButtonPermission(buttonKey);
  };

  /**
   * 检查是否为超级管理员
   */
  const isSuperAdmin = (): boolean => {
    return PermissionUtils.isSuperAdmin();
  };

  /**
   * 获取用户权限列表
   */
  const getUserPermissions = (): string[] => {
    return PermissionUtils.getUserPermissions();
  };

  return {
    hasMenuPermission,
    hasButtonPermission,
    isSuperAdmin,
    getUserPermissions
  };
}

/**
 * 权限指令（用于模板中的 v-permission）
 */
export const permissionDirective = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding;
    
    if (value) {
      const hasPermission = PermissionUtils.hasMenuPermission(value) || PermissionUtils.isSuperAdmin();
      
      if (!hasPermission) {
        el.style.display = 'none';
      }
    }
  },
  
  updated(el: HTMLElement, binding: any) {
    const { value } = binding;
    
    if (value) {
      const hasPermission = PermissionUtils.hasMenuPermission(value) || PermissionUtils.isSuperAdmin();
      
      el.style.display = hasPermission ? '' : 'none';
    }
  }
};
