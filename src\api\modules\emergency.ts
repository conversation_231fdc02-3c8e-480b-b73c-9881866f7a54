import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";

export namespace Emergency {
  export interface LogRes {
    id: number;
    title: string;
    /** 事件时间 */
    eventTime: string;
    remark: string;
    orgId: string;
    orgName: string;
  }

  export interface LogParams {
    title: string;
    /** 事件时间 */
    eventTime: string;
    remark: string;
    orgId: string;
  }
}

/**应急日志列表 */
export function getEmergencyLogList(params: ReqPage & { title: string }) {
  return http.get<ResPage<Emergency.LogRes>>(PORT1 + "/emergency/paging", params, { loading: true });
}

/** 新增日志 */
export function addEmergencyLog(params: Partial<Emergency.LogParams>) {
  return http.post(PORT1 + "/emergency", params, { loading: true });
}

/** 修改日志 */
export function editEmergencyLog(params: Partial<Emergency.LogParams> & { id: number }) {
  return http.put(PORT1 + "/emergency", params, { loading: true });
}
