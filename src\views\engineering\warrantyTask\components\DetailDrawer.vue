<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}任务`">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit" :disabled="drawerProps.isView">
      <el-form-item :error="errors.systemType" label="归属系统" required>
        <el-select v-bind="formData.systemType">
          <el-option v-for="item in systemTypeOptions" :key="item.value" v-bind="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.deviceId" label="设备" required>
        <el-select
          v-bind="formData.deviceId"
          filterable
          remote
          :loading="deviceLoading"
          :remote-method="(name: string) => refreshDevice(500, name)"
        >
          <el-option v-for="item in deviceList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.sourceType" label="来源" required>
        <el-select v-bind="formData.sourceType" disabled>
          <el-option v-for="item in sourceTypeOptions" :key="item.value" v-bind="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.location" label="报修位置" required>
        <el-input v-bind="formData.location" />
      </el-form-item>
      <el-form-item :error="errors.remark" label="问题说明" required>
        <el-input v-bind="formData.remark" type="textarea" />
      </el-form-item>
      <!-- <el-form-item :error="errors.bizType" label="关联业务类型" required>
        <el-select v-bind="formData.bizType">
          <el-option v-for="item in bizTypeOptions" :key="item.value" v-bind="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.bizId" label="关联业务" required>
        <el-select
          v-bind="formData.bizId"
          remote
          filterable
          :loading="planLoading"
          :remote-method="(name: string) => refreshPlan(500, name)"
        >
          <el-option v-for="item in planList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item :error="errors.attachments" label="附件">
        <UploadImg
          :image-url="formData.attachments.modelValue"
          @update:image-url="formData.attachments['onUpdate:modelValue']"
          width="135px"
          height="135px"
          :file-size="3"
        >
          <template #empty>
            <el-icon class="mr-1"><Plus /></el-icon>
            <span>请上传附件</span>
          </template>
          <template #tip> 图片大小不能超过 3M </template>
        </UploadImg>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="onSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="SparePartsDetailDrawer">
import { useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string, number } from "yup";
import { systemTypeOptions, getDeviceList } from "@/api/modules/device";
import { sourceTypeOptions, WarrantyTask } from "@/api/modules/warrantyTask";
import UploadImg from "@/components/Upload/Img.vue";

interface DrawerProps {
  title?: "新增" | "编辑" | "查看";
  isView: boolean;
  row: Partial<WarrantyTask.TaskRes>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    systemType: number().required().label("归属系统"),
    /* 设备ID */
    deviceId: string().required().label("设备"),
    /* 附件URL,数组 */
    attachments: string().label("附件"),
    /* 来源 */
    sourceType: number().required().label("来源"),
    // /* 关联业务 */
    // bizType: number().required().label("关联业务类型"),
    // /* 关联业务ID */
    // bizId: number().required().label("关联业务ID"),
    location: string().required().max(30).label("报修位置"),
    /* 备注 */
    remark: string().required().label("问题说明")
  })
);

const { defineComponentBinds, handleSubmit, errors, setValues, resetForm, setFieldValue } = useForm<
  Partial<WarrantyTask.TaskParams>
>({
  validationSchema: schema,
  initialValues: {
    sourceType: WarrantyTask.SourceType.PC
  }
});

const formData = ref({
  systemType: defineComponentBinds("systemType", elPlusConfig),
  /* 设备ID */
  deviceId: defineComponentBinds("deviceId", elPlusConfig),
  /* 附件URL,数组 */
  attachments: defineComponentBinds("attachments", elPlusConfig),
  /* 来源 */
  sourceType: defineComponentBinds("sourceType", elPlusConfig),
  /* 关联业务 */
  // bizType: defineComponentBinds("bizType", elPlusConfig),
  /* 关联业务ID */
  // bizId: defineComponentBinds("bizId", elPlusConfig),
  /* 备注 */
  location: defineComponentBinds("location", elPlusConfig),
  remark: defineComponentBinds("remark", elPlusConfig)
});

// 设备列表
const {
  state: deviceList,
  execute: refreshDevice,
  isLoading: deviceLoading
} = useAsyncState(async (name?: string) => {
  if (!formData.value.systemType.modelValue) return [];

  const res = await getDeviceList(
    {
      name: name ? name : undefined,
      systemType: formData.value.systemType.modelValue,
      pageIndex: 1,
      pageSize: 100
    },
    false
  );
  return res.data.records;
}, []);

watch(
  () => formData.value.systemType.modelValue,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      setFieldValue("deviceId", undefined);
      refreshDevice(0);
    }
  }
);

// 关联计划
// const {
//   state: planList,
//   execute: refreshPlan,
//   isLoading: planLoading
// } = useAsyncState(async (name?: string) => {
//   if (!formData.value.bizType.modelValue) {
//     return [];
//   }
//   const getAPi =
//     formData.value.bizType.modelValue === WarrantyTask.BizType.InspectionPlanTask ? getPlanPatrolList : getPlanMaintenanceList;
//   const res = await getAPi(
//     {
//       name: name ? name : undefined,
//       pageIndex: 1,
//       pageSize: 100
//     },
//     false
//   );
//   return res.data.records;
// }, []);
// watch(
//   () => formData.value.bizType.modelValue,
//   (newValue, oldValue) => {
//     if (newValue !== oldValue) {
//       setFieldValue("bizId", undefined);
//       refreshPlan(0);
//     }
//   }
// );

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: undefined,
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  if (params.title !== "新增") {
    setValues(params.row);
  } else {
    resetForm();
  }

  drawerVisible.value = true;
};

const onSubmit = handleSubmit(async values => {
  console.log(values);
  await drawerProps.value.api!(values);
  drawerVisible.value = false;
  drawerProps.value.getTableList!();
});

defineExpose({
  acceptParams
});
</script>
