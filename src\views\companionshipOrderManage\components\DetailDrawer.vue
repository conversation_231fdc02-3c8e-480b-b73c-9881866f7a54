<template>
  <el-drawer
    v-model="companionshipOrderStore.drawerVisible"
    :destroy-on-close="true"
    size="600px"
    :title="`${companionshipOrderStore.drawerParams?.title}陪护单`"
  >
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" :disabled="isView" @submit="onSubmit">
      <el-form-item :error="errors.accountPhone" label="下单人手机号" required>
        <el-input v-bind="formData.accountPhone" />
      </el-form-item>
      <el-form-item :error="errors.serverType" label="陪护单类型" required>
        <el-select
          v-bind="formData.serverType"
          :disabled="orderStatus != null && orderStatus !== CompanionshipOrder.OrderStatus.BEndReview"
        >
          <el-option v-for="item in serverTypeOptions" :key="item.value" v-bind="item">{{ item.label }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.patientName" label="病人姓名" required>
        <el-input v-bind="formData.patientName" />
      </el-form-item>
      <el-form-item :error="errors.patientGender" label="病人性别" required>
        <el-select v-bind="formData.patientGender">
          <el-option v-for="item in genderType" :key="item.value" v-bind="item">{{ item.label }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.patientBirthday" label="出生日期">
        <el-date-picker type="date" v-bind="formData.patientBirthday" value-format="YYYY-MM-DD" />
      </el-form-item>
      <el-form-item :error="errors.phone" label="家属联系电话" required>
        <el-input v-bind="formData.phone" />
      </el-form-item>
      <div v-show="formData.serverType.modelValue === ServerType.HospitalCare || !formData.serverType.modelValue == undefined">
        <el-form-item :error="errors.orgId" label="医院" required>
          <el-select
            @change="changeOrgId"
            v-bind="formData.orgId"
            :disabled="orderStatus != null && orderStatus !== CompanionshipOrder.OrderStatus.BEndReview"
          >
            <el-option v-for="item in orgList" :key="item.id" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :error="errors.departments" label="科室" required>
          <el-select
            v-bind="formData.departments"
            :disabled="orderStatus != null && orderStatus !== CompanionshipOrder.OrderStatus.BEndReview"
          >
            <el-option v-for="item in currentHospitalList" :key="item.id" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :error="errors.bedNo" label="床号">
          <el-input v-bind="formData.bedNo" />
        </el-form-item>
      </div>
      <el-form-item :error="errors.time" label="预计陪护时间" required>
        <el-date-picker
          type="datetimerange"
          :disabled="orderStatus != null && orderStatus !== CompanionshipOrder.OrderStatus.BEndReview"
          v-bind="formData.time"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="defaultTime"
          format="YYYY-MM-DD HH 时"
          time-format="HH 时"
          value-format="x"
        />
      </el-form-item>
      <el-form-item :error="errors.realTime" label="实际陪护时间" v-show="formData.realTime.modelValue">
        <el-date-picker
          type="datetimerange"
          v-bind="formData.realTime"
          disabled
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="defaultTime"
          format="YYYY-MM-DD HH 时"
          time-format="HH 时"
          value-format="x"
        />
      </el-form-item>
      <!-- <el-form-item label="陪护实际开始日期" required>
        <el-input v-bind="formData.realStartTime" />
      </el-form-item>
      <el-form-item label="陪护实际终止日期" required>
        <el-input v-bind="formData.realEndTime" />
      </el-form-item> -->
      <el-form-item :error="errors.patientStateDesc" label="病人病情描述">
        <el-input v-bind="formData.patientStateDesc" type="textarea" />
      </el-form-item>
      <el-form-item :error="errors.specificDesc" label="特殊要求">
        <el-input v-bind="formData.specificDesc" type="textarea" />
      </el-form-item>
      <el-form-item :error="errors.address" label="详细地址">
        <el-input v-bind="formData.address" type="textarea" />
      </el-form-item>
      <el-form-item :error="errors.remark" label="备注">
        <el-input v-bind="formData.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="companionshipOrderStore.drawerVisible = false">取消</el-button>
      <el-button type="primary" native-type="submit" @click="onSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="DetailDrawer">
import { ServerType, genderType, serverTypeOptions } from "@/utils/dict";
import { useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string, array, number } from "yup";
import { CompanionshipOrder, createCompanionshipOrder, updateCompanionshipOrder } from "@/api/modules/companionshipOrder";
import { useCompanionshipOrderStore } from "@/stores/modules/companionshipOrder";
import { phoneRegexp } from "@/utils/eleValidate";
import { useOrgStore } from "@/stores/modules/orgId";
import { getDepartmentListByHospitalId } from "@/api/modules/organization";

const companionshipOrderStore = useCompanionshipOrderStore();
/**
 * 陪护单状态
 */
const orderStatus = toRef(() => companionshipOrderStore.drawerParams?.row?.orderStatus);
const defaultTime = new Date(2000, 1, 1, 12, 0, 0);
const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    accountPhone: string().required().matches(phoneRegexp, "请输入正确的手机号").label("下单人手机号"),
    serverType: number().required().label("陪护单类型"),
    patientName: string().required().label("病人姓名"),
    patientGender: number().required().label("病人性别"),
    patientBirthday: string().label("病人出生日期"),
    phone: string().required().matches(phoneRegexp, "请输入正确的手机号").label("家属联系电话"),
    orgId: string().when("serverType", ([serverType], schema) => {
      return serverType !== ServerType.HospitalCare ? schema.nullable() : schema.required("医院是必填项").label("医院");
    }),
    departments: string().when("serverType", ([serverType], schema) => {
      return serverType !== ServerType.HospitalCare ? schema : schema.required("科室是必填项").label("科室");
    }),
    bedNo: string().label("床号"),
    time: array().required().length(2).of(number()).label("预计陪护时间"),
    realTime: array().nullable().length(2).of(number()).label("实际陪护时间"),
    // startTime: string().required().label("陪护开始日期"),
    // endTime: string().required().label("陪护终止日期"),
    // realStartTime: string().required().label("陪护实际开始日期"),
    // realEndTime: string().required().label("陪护实际终止日期"),
    patientStateDesc: string().nullable().label("病人病情描述"),
    specificDesc: string().label("特殊要求"),
    address: string().label("详细地址"),
    remark: string().label("备注")
  })
);

type FormData = Omit<Partial<CompanionshipOrder.Params>, "startTime" | "endTime" | "realStartTime" | "realEndTime"> & {
  time: [number, number];
  realTime?: [number, number];
};

const { defineComponentBinds, handleSubmit, resetForm, errors, setValues, setFieldValue } = useForm<FormData>({
  validationSchema: schema,
  initialValues: {
    serverType: ServerType.HospitalCare
  }
});
const isView = toRef(() => companionshipOrderStore.drawerParams?.isView);

// 抽屉开关监听
watch(
  () => companionshipOrderStore.drawerVisible,
  val => {
    // 重置表单
    if (!val && companionshipOrderStore.drawerParams?.title !== "新增") {
      resetForm();
    } else if (val && companionshipOrderStore.drawerParams?.title !== "新增" && companionshipOrderStore.drawerParams?.row) {
      const values: FormData = {
        ...companionshipOrderStore.drawerParams.row,
        time: [
          Number(companionshipOrderStore.drawerParams.row.startTime),
          Number(companionshipOrderStore.drawerParams.row.endTime)
        ],
        realTime: undefined
      };
      if (companionshipOrderStore.drawerParams.row.realEndTime && companionshipOrderStore.drawerParams.row.realStartTime) {
        values.realTime = [
          Number(companionshipOrderStore.drawerParams.row.realStartTime),
          Number(companionshipOrderStore.drawerParams.row.realEndTime)
        ];
      }
      setValues(values);
    }
  }
);

const formData = ref({
  accountPhone: defineComponentBinds("accountPhone", elPlusConfig),
  serverType: defineComponentBinds("serverType", elPlusConfig),
  patientName: defineComponentBinds("patientName", elPlusConfig),
  patientGender: defineComponentBinds("patientGender", elPlusConfig),
  patientBirthday: defineComponentBinds("patientBirthday", elPlusConfig),
  phone: defineComponentBinds("phone", elPlusConfig),
  orgId: defineComponentBinds("orgId", elPlusConfig),
  departments: defineComponentBinds("departments", elPlusConfig),
  bedNo: defineComponentBinds("bedNo", elPlusConfig),
  time: defineComponentBinds("time", elPlusConfig),
  realTime: defineComponentBinds("realTime", elPlusConfig),
  // startTime: defineComponentBinds("startTime", elPlusConfig),
  // endTime: defineComponentBinds("endTime", elPlusConfig),
  // realStartTime: defineComponentBinds("realStartTime", elPlusConfig),
  // realEndTime: defineComponentBinds("realEndTime", elPlusConfig),
  patientStateDesc: defineComponentBinds("patientStateDesc", elPlusConfig),
  specificDesc: defineComponentBinds("specificDesc", elPlusConfig),
  address: defineComponentBinds("address", elPlusConfig),
  remark: defineComponentBinds("remark", elPlusConfig)
});

function changeOrgId() {
  setFieldValue("departments", undefined);
}

const onSubmit = handleSubmit(async values => {
  console.log("Submitted with", values);
  const params: Partial<typeof values & CompanionshipOrder.Params> = {
    ...values,
    startTime: values.time[0]!,
    endTime: values.time[1]!,
    realStartTime: values.realTime ? values.realTime[0] : undefined,
    realEndTime: values.realTime ? values.realTime[1] : undefined
  };

  delete params.realTime;
  delete params.time;

  if (params.serverType !== ServerType.HospitalCare) {
    delete params.bedNo;
    delete params.orgId;
    delete params.departments;
  }

  if (companionshipOrderStore.drawerParams?.title === "新增") {
    await createCompanionshipOrder(params);
  } else {
    await updateCompanionshipOrder(params);
  }

  // 关闭并刷新
  companionshipOrderStore.drawerVisible = false;
  companionshipOrderStore.getTableList!();
});

/**
 * 医院列表
 */
const orgStore = useOrgStore();
const orgList = toRef(orgStore, "hospitalList");
// const orgTree = toRef(orgStore, "orgTree");

const currentHospitalList = computedAsync(async () => {
  const currentHospital = formData.value.orgId.modelValue;
  if (!currentHospital) return [];
  const { data } = await getDepartmentListByHospitalId(currentHospital);
  if (data) return data;
});
</script>
