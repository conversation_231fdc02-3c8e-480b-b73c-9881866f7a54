<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增角色</el-button>
        <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除
        </el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="View" @click="openDrawer('查看', scope.row)">查看</el-button>
        <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button type="primary" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </ProTable>
    <roleDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="roleManage">
import { Role, getRoleList, addRole, editRole, getRoleDetail, deleteRole, getRoleMenu } from "@/api/modules/role";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { CirclePlus, Delete, EditPen, View } from "@element-plus/icons-vue";
import roleDrawer from "./components/DetailDrawer.vue";
import dayjs from "dayjs";
import { useHandleData } from "@/hooks/useHandleData";
import { StatusEnum } from "@/utils/dict";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

/**
 * 查询列表参数
 */
const getTableList = (params: any) => {
  return getRoleList(params);
};

const renderTime = (time: string) => {
  return (
    <div>
      <span>{dayjs(time).format("YYYY-MM-DD HH:mm:ss")}</span>
    </div>
  );
};

const columns = reactive<ColumnProps<Role.ResRole>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "name",
    label: "角色名",
    search: { el: "input" }
  },
  {
    prop: "roleKey",
    label: "角色权限字符"
  },
  {
    prop: "status",
    label: "角色状态",
    render: scope => {
      return (
        <el-switch model-value={scope.row.status} active-value={1} inactive-value={0} onChange={() => changeStatus(scope.row)} />
      );
    }
  },
  {
    prop: "createdAt",
    label: "创建时间",
    render: scope => renderTime(scope.row.createdAt)
  },
  {
    prop: "createdAt",
    label: "更新时间",
    render: scope => renderTime(scope.row.createdAt)
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);

/**
 * 打开 drawer(新增、查看、编辑)
 */
const drawerRef = ref<InstanceType<typeof roleDrawer> | null>(null);
async function openDrawer(title: string, row: Partial<Role.ResRole> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: title === "新增" ? addRole : title === "编辑" ? editRole : undefined,
    getTableList: proTable.value?.getTableList
  };
  if (title !== "新增" && row.id) {
    const res = await getRoleDetail(row.id);
    const { data } = await getRoleMenu(row.id);
    res.data.menuIds = data.menuList.map(item => item.id);
    params.row = res.data;
  }
  drawerRef.value?.acceptParams(params);
}

/**
 * 删除角色信息
 */
const handleDelete = async (params: Role.ResRole) => {
  await useHandleData(deleteRole, [params.id], `删除【${params.name}】角色`);
  proTable.value?.getTableList();
};

/**
 * 批量删除角色信息
 */
async function batchDelete(selectedListIds: string[]) {
  await useHandleData(deleteRole, selectedListIds, "删除所选角色信息");
  proTable.value?.getTableList();
}

/**
 * 禁用启用角色
 * @param row
 */
async function changeStatus(row: Role.ResRole) {
  if (row.status === StatusEnum.NO) {
    await useHandleData(editRole, { ...row, status: StatusEnum.OFF }, `禁用【${row.name}】角色`);
  } else {
    await useHandleData(editRole, { ...row, status: StatusEnum.NO.valueOf() }, `启用【${row.name}】角色`);
  }

  proTable.value?.getTableList();
}
</script>
