import { StatusEnum } from "@/utils/dict";

// 请求响应参数（不包含data）
export interface Result {
  code: string;
  msg: string;
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  data: T;
}

// 分页响应参数
export interface ResPage<T> {
  records: T[];
  current: number;
  size: number;
  total: number;
}

// 分页请求参数
export interface ReqPage {
  pageIndex: number;
  pageSize: number;
}

// 文件上传模块
export namespace Upload {
  export interface ResFileUrl {
    fileUrl: string;
  }
}

// 登录模块
export namespace Login {
  export interface ReqLoginForm {
    phoneNumber: string;
    password: string;
  }
  export interface ResLogin {
    tokenName: string;
    tokenValue: string;
  }
  export interface ResAuthButtons {
    [key: string]: string[];
  }
}

// 用户管理模块
export namespace User {
  export interface ReqUserParams {
    organizationIds?: string[];
    departmentIds?: string[];
    id?: string;
    password?: string;
    nickname: string;
    phoneNumber: string;
    avatarUrl?: string;
    status: StatusEnum;
    type?: number;
  }
  export interface ResUserList {
    id: string;
    nickname: string;
    phoneNumber: string;
    avatarUrl?: string;
    status: StatusEnum;
    type: number;
    lastLoginIp: string;
    lastLoginDate: string;
    passwordUpdateDate: string;
    createBy: string;
    updateBy: string;
    createdAt: string;
    updatedAt: string;
  }

  export interface UserOrg {
    id: string;
    name: string;
    parentId: string;
    level: number;
    status: StatusEnum;
  }

  export interface UserRole {
    id: string;
    name: string;
    roleKey: string;
    status: StatusEnum;
    remark: string;
  }
}

// 院区管理模块
export namespace Hospital {
  export interface ReqHospitalParams {
    id?: string;
    name: string;
    address: string;
    remark?: string;
    enable?: number;
  }

  export interface ResHospitalList {
    id: string;
    name: string;
    address: string;
    remark: string;
    enable: number;
    deleted: number;
    createBy: string;
    createdAt: string;
    updateBy: string;
    updatedAt: string;
  }

  export interface HospitalSearchParams {
    name?: string;
    address?: string;
    enable?: number;
  }
}
