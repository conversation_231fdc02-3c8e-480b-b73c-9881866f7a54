<template>
  <div class="card content-box">
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      label-suffix=" :"
      :disabled="isView"
      :rules="rules"
      :model="userDetail"
      :validate-on-rule-change="false"
    >
      <el-form-item label="用户头像" prop="avatarUrl">
        <UploadImg v-model:image-url="userDetail.avatarUrl" width="135px" height="135px" :file-size="3">
          <template #empty>
            <el-icon><Avatar /></el-icon>
            <span>请上传头像</span>
          </template>
          <template #tip> 头像大小不能超过 3M </template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="用户名" prop="nickname">
        <el-input v-model="userDetail.nickname" placeholder="请填写用户名"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input v-model="userDetail.phoneNumber" placeholder="请填写用户手机号"></el-input>
      </el-form-item>
      <el-form-item label="用户角色" prop="role">
        <el-select
          class="w-full"
          v-model="userDetail.role"
          multiple
          filterable
          remote
          reserve-keyword
          placeholder="请选择用户角色"
          :remote-method="remoteRule"
          :loading="releOptionLoading"
        >
          <el-option v-for="item in roleOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户所属医院" prop="hospitalId">
        <el-select
          class="w-full"
          v-model="userDetail.hospitalId"
          placeholder="请选择用户所属医院"
          clearable
          :disabled="routeName === 'accountEdit'"
        >
          <el-option v-for="item in hospitalList" :key="item.id" :value="String(item.id)" :label="item.name"></el-option>
        </el-select>
        <!-- 新增时显示备注 -->
        <div v-if="routeName === 'accountAdd'" class="form-tip">
          <el-icon class="tip-icon"><InfoFilled /></el-icon>
          <span class="tip-text">院区绑定后不可修改</span>
        </div>
      </el-form-item>
      <el-form-item label="用户所属部门" prop="departmentIds">
        <el-select
          class="w-full"
          v-model="userDetail.departmentIds"
          multiple
          placeholder="请选择用户部门"
          :render-after-expand="false"
        >
          <el-option v-for="item in departmentList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <template v-if="route.name === 'accountAdd'">
        <el-form-item label="密码" prop="password">
          <el-input type="password" v-model="userDetail.password" show-password placeholder="请填写密码"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input type="password" v-model="userDetail.confirmPassword" show-password placeholder="请重新填写密码"></el-input>
        </el-form-item>
      </template>
      <el-form-item label="状态" prop="status">
        <el-switch v-model="userDetail.status" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.form-tip {
  display: flex;
  align-items: center;
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
}

.tip-icon {
  margin-right: 4px;
  font-size: 14px;
}

.tip-text {
  line-height: 1;
}
</style>

<script setup lang="ts" name="UserDetail">
import UploadImg from "@/components/Upload/Img.vue";
import { User, Hospital } from "@/api/interface";
import { getUserDetail, editUser, userSetRole, addUser } from "@/api/modules/user";
import { getRoleList, Role } from "@/api/modules/role";
import { useTabsStore } from "@/stores/modules/tabs";
import type { ElForm, FormRules } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import { useOrgStore } from "@/stores/modules/orgId";
import { getResourceData } from "@/api/modules/hospital";

// 基础验证规则
const baseRules: FormRules = {
  nickname: [{ required: true, message: "请填写用户姓名" }],
  phoneNumber: [{ required: true, message: "请填写电话号" }],
  role: [{ required: true, message: "请选择用户角色" }],
  hospitalId: [{ required: true, message: "请选择用户所属医院" }],
  departmentIds: [
    {
      validator: orgValidator
    }
  ]
};

// 动态验证规则，只有在编辑模式或已提交时才显示错误
const rules = computed<FormRules>(() => {
  const shouldValidate = routeName !== "accountAdd" || hasSubmitted.value;

  if (!shouldValidate) {
    return {}; // 新增模式且未提交时，不显示任何验证规则
  }

  const currentRules = { ...baseRules };

  // 新增模式需要密码验证
  if (routeName === "accountAdd") {
    currentRules.password = [{ required: true, message: "请填写密码" }];
    currentRules.confirmPassword = [
      { required: true, message: "请输入密码", trigger: "blur" },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value !== userDetail.value.password) {
            callback(new Error("确认密码不一致"));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ];
  }

  return currentRules;
});

const userDetail = ref<
  User.ReqUserParams & {
    confirmPassword?: string;
    role?: string[];
    hospitalId?: string;
  }
>({
  nickname: "",
  phoneNumber: "",
  avatarUrl: "",
  type: 1,
  status: 1,
  password: "",
  organizationIds: [],
  departmentIds: [],
  role: [],
  hospitalId: "",
  confirmPassword: ""
});

function orgValidator(rule: any, value: string, callback: any) {
  if (userDetail.value.departmentIds && userDetail.value.departmentIds.length !== 0) {
    callback();
  } else {
    callback(new Error("物业部门必须填一个"));
  }
}

const tabStore = useTabsStore();
const route = useRoute();
const routeName = route.name as "accountAdd" | "accountDetail" | "accountEdit";
const isView = ref(false);

// 控制是否显示验证错误（只有在提交后才显示）
const hasSubmitted = ref(false);

async function init() {
  // 获取院区数据
  await getHospitalList();

  if (route.params.id && typeof route.params.id === "string") {
    const { data } = await getUserDetail(route.params.id);
    console.log("获取到的用户详情:", data);
    console.log("用户的hospitalId:", data.hospitalId);
    console.log("当前院区列表:", hospitalList.value);

    // isView.value = true;
    userDetail.value = {
      id: data.id,
      nickname: data.nickname,
      phoneNumber: data.phoneNumber,
      avatarUrl: data.avatarUrl,
      type: data.type,
      status: data.status,
      role: data.roleList?.map(item => item.id) || [],
      organizationIds: data.organizationList?.map(item => item.id) || [],
      departmentIds: data.departmentList?.map(item => item.id) || [],
      hospitalId: data.hospitalId ? data.hospitalId.toString() : ""
    };

    console.log("设置后的userDetail.hospitalId:", userDetail.value.hospitalId);

    if (routeName === "accountDetail") {
      tabStore.setTabsTitle(`${data.nickname}-详情`);
      isView.value = true;
    } else if (routeName === "accountEdit") {
      tabStore.setTabsTitle(`${data.nickname}-编辑`);
    }
  }


}

/**
 * 医院、部门列表
 */
const orgStore = useOrgStore();
const orgList = toRef(orgStore, "hospitalList");
const departmentList = toRef(orgStore, "departmentList");

/**
 * 院区列表
 */
const hospitalList = ref<Hospital.ResResourceData[]>([]);

/**
 * 角色列表
 */
const {
  state: roleOptions,
  isLoading: releOptionLoading,
  execute: refreshRole
} = useAsyncState<Role.ResRole[], string[]>(async (name: string) => {
  const params: {
    pageIndex: number;
    pageSize: number;
    name?: string;
  } = {
    pageIndex: 1,
    pageSize: 100
  };
  if (name) {
    params.name = name;
  }

  const { data } = await getRoleList(params, false);
  return data.records;
}, []);

const remoteRule = useDebounceFn((name: string) => {
  refreshRole(0, name);
}, 500);

/**
 * 获取院区数据
 */
async function getHospitalList() {
  try {
    const { data } = await getResourceData();
    hospitalList.value = data;
    console.log("获取到的院区数据:", data);
  } catch (error) {
    console.error("获取院区数据失败:", error);
  }
}

init();

const ruleFormRef = ref<InstanceType<typeof ElForm>>();
/**
 * 提交表单
 */
function onSubmit() {
  // 标记已经尝试提交，开始显示验证错误
  hasSubmitted.value = true;

  ruleFormRef.value?.validate(async isValid => {
    if (!isValid) {
      return;
    }
    const params = { ...userDetail.value };

    const roleIds = params.role || [];
    delete params.role;

    // hospitalId 直接使用字符串格式，不需要转换
    // 保留 hospitalId 字段，移除 organizationIds
    delete params.organizationIds;

    if (routeName === "accountEdit") {
      await Promise.all([
        editUser(params),
        userSetRole({
          accountId: userDetail.value.id!,
          roleIds
        })
      ]);
    } else if (routeName === "accountAdd") {
      delete params.confirmPassword;

      const { data } = await addUser(params);

      userSetRole({
        accountId: data.accountId,
        roleIds
      });
    }

    tabStore.removeTabs(route.fullPath);
  });
}
</script>

<style scoped>
.el-form {
  width: 100%;
  margin-top: 20px;
}
</style>
