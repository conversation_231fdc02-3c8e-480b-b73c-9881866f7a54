<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getOrderEvaluation"></ProTable>
  </div>
</template>

<script setup lang="tsx" name="cashArchiving">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { getOrderEvaluation, Evaluation, sourceOptions } from "@/api/modules/orderEvaluation";
import { useTabsStore } from "@/stores/modules/tabs";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();
const route = useRoute();
const tabStore = useTabsStore();

const defaultOrderId = route.query.orderId && typeof route.query.orderId === "string" ? route.query.orderId : "";
if (defaultOrderId) {
  tabStore.setTabsTitle(`订单评价-${defaultOrderId}`);
}

const columns = reactive<ColumnProps<Evaluation.Record>[]>([
  {
    prop: "orderId",
    label: "陪护单号",
    width: 200,
    search: { el: "input", defaultValue: defaultOrderId }
  },
  {
    prop: "level",
    label: "评价等级",
    width: 150
  },
  {
    prop: "source",
    label: "评价方",
    enum: sourceOptions,
    tag: true
  },
  {
    prop: "remark",
    label: "评价内容",
    width: 200
  }
]);
</script>
