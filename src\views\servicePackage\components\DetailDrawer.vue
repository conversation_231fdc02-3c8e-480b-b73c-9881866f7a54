<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="550px" :title="`${drawerProps.title}套餐`">
    <el-form
      ref="ruleFormRef"
      label-width="160px"
      label-suffix=" :"
      :rules="rules"
      :validate-on-rule-change="false"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <el-form-item label="封面" prop="coverPicture">
        <UploadImg v-model:image-url="drawerProps.row.coverPicture" width="135px" height="135px" :file-size="3">
          <template #empty>
            <el-icon><Avatar /></el-icon>
            <span>请上传封面图</span>
          </template>
          <template #tip> 封面大小不能超过 3M </template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="套餐名称" prop="name">
        <el-input v-model="drawerProps.row!.name" placeholder="请填写套餐名称"></el-input>
      </el-form-item>
      <el-form-item label="套餐类型" prop="serverType">
        <el-select v-model="drawerProps.row!.serverType">
          <el-option v-for="item in drawerProps.serverType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="套餐星级" prop="nursingStar">
        <el-rate v-model="drawerProps.row!.nursingStar" clearable placeholder="请填写套餐名称"></el-rate>
      </el-form-item>
      <el-form-item label="适用院区" prop="orgIdList" v-if="drawerProps.row!.serverType === ServerType.HospitalCare">
        <el-select v-model="drawerProps.row!.orgIdList" multiple>
          <el-option v-for="item in drawerProps.orgList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="服务价格（天）" prop="price">
        <el-input v-model="drawerProps.row!.price" type="number" :disabled="drawerProps.title !== '新增'">
          <template #prepend>￥</template>
        </el-input>
      </el-form-item>
      <el-form-item label="计费终止时间" prop="billingEndTime">
        <el-time-select
          v-model="drawerProps.row!.billingEndTime"
          start="00:00"
          step="01:00"
          end="23:00"
          :disabled="drawerProps.title !== '新增'"
          placeholder="选择计费截至时间"
        />
      </el-form-item>
      <el-form-item label="物业管理费比例" prop="rateCertifiedProperty">
        <!-- <el-input-number
          :disabled="drawerProps.title !== '新增'"
          :min="0"
          :max="100"
          :precision="2"
          v-model="drawerProps.row!.rateCertifiedProperty"
          controls-position="right"
        ></el-input-number> -->
        <el-input v-model="drawerProps.row!.rateCertifiedProperty" :disabled="drawerProps.title !== '新增'">
          <template #append>%</template>
        </el-input>
      </el-form-item>
      <el-form-item label="护工费比例" prop="rateNursing">
        <!-- <el-input-number
          :disabled="drawerProps.title !== '新增'"
          :min="0"
          :max="100"
          :precision="2"
          v-model="drawerProps.row!.rateNursing"
          controls-position="right"
        ></el-input-number> -->
        <el-input v-model="drawerProps.row!.rateNursing" :disabled="drawerProps.title !== '新增'">
          <template #append>%</template>
        </el-input>
      </el-form-item>
      <el-form-item label="院方管理费比例" prop="rateHospital">
        <!-- <el-input-number
          :disabled="drawerProps.title !== '新增'"
          :min="0"
          :max="100"
          :precision="2"
          v-model="drawerProps.row!.rateHospital"
          controls-position="right"
        ></el-input-number> -->
        <el-input v-model="drawerProps.row!.rateHospital" :disabled="drawerProps.title !== '新增'">
          <template #append>%</template>
        </el-input>
      </el-form-item>
      <el-form-item label="详细描述" prop="remark">
        <el-input v-model="drawerProps.row!.remark" type="textarea" placeholder="请填写详细描述" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="DetailDrawer">
import { BigNumber } from "bignumber.js";
import UploadImg from "@/components/Upload/Img.vue";
import type { FormInstance, ElSelect, FormRules } from "element-plus";
import { ServerPackage } from "@/api/modules/servicePackage";
import { ServerType } from "@/utils/dict";
import { priceToCent, roundPriceByThousand } from "@/utils";

const rules = reactive<FormRules>({
  coverPicture: [{ required: true, message: "请上传封面图" }],
  name: [{ required: true, message: "请填写套餐名称" }],
  serverType: [{ required: true, message: "请选择套餐类型" }],
  orgIdList: [],
  price: [
    { required: true, message: "请填写服务价格" },
    {
      type: "number",
      asyncValidator: (rule, value) => {
        if (value <= 0) {
          return Promise.reject(new Error("服务价格必须大于0"));
        } else if (value.toString().split(".")[1]?.length > 2) {
          return Promise.reject(new Error("服务价格只保留两位小数"));
        }
        return Promise.resolve();
      }
    }
  ],
  billingEndTime: [{ required: true, message: "请选择计费终止时间" }],
  rateCertifiedProperty: [{ required: true, message: "请填写物业管理费比例" }, { validator: proportionValidator }],
  rateNursing: [{ required: true, message: "请填写护工费比例" }, { validator: proportionValidator }],
  rateHospital: [{ required: true, message: "请填写院方管理费比例" }, { validator: proportionValidator }]
});

function proportionValidator(rule: any, value: string, callback: any) {
  if (
    ruleFormRef.value &&
    drawerProps.value.row &&
    drawerProps.value.row.rateCertifiedProperty != null &&
    drawerProps.value.row.rateNursing != null &&
    drawerProps.value.row.rateHospital != null
  ) {
    const rateCertifiedProperty = new BigNumber(drawerProps.value.row.rateCertifiedProperty);
    const rateNursing = new BigNumber(drawerProps.value.row.rateNursing);
    const rateHospital = new BigNumber(drawerProps.value.row.rateHospital);
    if (rateCertifiedProperty.plus(rateNursing).plus(rateHospital).toNumber() !== 100) {
      callback(new Error("物业管理费比例、护工费比例和院方管理费比例之和必须等于100"));
    } else {
      ruleFormRef.value.clearValidate(["rateNursing", "rateHospital", "rateCertifiedProperty"]);
    }
  }
  callback();
}

interface Option {
  label: string;
  value: string | number;
}

interface DrawerProps {
  title: string;
  isView: boolean;
  serverType: Option[];
  orgList: Option[];
  row: Partial<ServerPackage.ServerPackageParams & { billingEndTime: string }>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  serverType: [],
  orgList: [],
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
};

watch(
  () => drawerProps.value.row.serverType,
  value => {
    if (value === ServerType.HospitalCare) {
      rules.orgIdList = [{ required: true, message: "请选择适用院区" }];
    } else {
      rules.orgIdList = [];
    }
  }
);

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    const data = { ...drawerProps.value.row };
    data.chargingTime = Number(data.billingEndTime?.split(":")[0]);
    delete data.billingEndTime;
    data.price = priceToCent(data.price);
    data.rateCertifiedProperty = roundPriceByThousand(data.rateCertifiedProperty);
    data.rateHospital = roundPriceByThousand(data.rateHospital);
    data.rateNursing = roundPriceByThousand(data.rateNursing);

    await drawerProps.value.api!(data);
    ElMessage.success({ message: `${drawerProps.value.title}用户成功！` });
    drawerProps.value.getTableList!();
    drawerVisible.value = false;
  });
};

defineExpose({
  acceptParams
});
</script>
