<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getWarrantyTaskList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">增加任务</el-button>
        <el-button type="primary" :icon="Download" @click="handleExport">导出</el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Edit" @click="goDetail(scope.row.id)">查看详情</el-button>
        <el-button v-if="scope.row.status < 3" type="primary" link @click="assignTasks(scope.row)"> 分派执行人员 </el-button>
        <el-button
          v-if="scope.row.status === WarrantyTask.Status.PendingReview"
          type="primary"
          link
          :icon="Stamp"
          @click="handlePass(scope.row)"
          >通过
        </el-button>
        <el-button
          v-if="scope.row.status === WarrantyTask.Status.PendingReview"
          type="primary"
          link
          :icon="Stamp"
          @click="handleReject(scope.row)"
          >驳回
        </el-button>
      </template>
    </ProTable>
    <DetailDrawer ref="drawerRef" />
    <AssignTasks ref="assignTask" />
  </div>
</template>

<script setup lang="tsx" name="warrantyTask">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { systemTypeOptions } from "@/api/modules/device";
import {
  getWarrantyTaskList,
  statusOptions,
  WarrantyTask,
  addWarrantyTask,
  editWarrantyTask,
  audit,
  assignExecutor,
  exportRepair
} from "@/api/modules/warrantyTask";
import { CirclePlus, Edit, Stamp, Download } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import DetailDrawer from "./components/DetailDrawer.vue";
import AssignTasks from "./components/AssignTasks.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { useDownload } from "@/hooks/useDownload";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<WarrantyTask.TaskRes>[]>([
  {
    prop: "code",
    label: "报修单编号",
    width: 200,
    search: { el: "input" }
  },
  {
    prop: "systemType",
    label: "报修单类型",
    enum: systemTypeOptions,
    tag: true
  },
  {
    prop: "status",
    label: "报修单状态",
    enum: statusOptions,
    tag: true,
    search: { el: "select" }
  },
  {
    prop: "createByName",
    label: "报修人员"
  },
  {
    prop: "urgencyDegreeType",
    label: "紧急程度",
    render: ({ row }) => {
      if (row.urgencyDegreeType == WarrantyTask.UrgencyDegree.Normal) return <el-tag type="warning">非紧急</el-tag>;
      else if (row.urgencyDegreeType == WarrantyTask.UrgencyDegree.Urgent) return <el-tag type="danger">紧急</el-tag>;
      else return "--";
    }
  },
  {
    prop: "createAt",
    label: "报修时间",
    render: ({ row }) => {
      return dayjs(row.createAt).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  {
    prop: "completedTime",
    label: "完成时间",
    render: ({ row }) => {
      return row.completedTime ? dayjs(row.completedTime).format("YYYY-MM-DD HH:mm:ss") : "--";
    }
  },
  {
    prop: "assignerAccountName",
    label: "派工人员"
  },
  {
    prop: "location",
    label: "报修位置"
  },
  {
    prop: "remark",
    label: "问题说明",
    width: 240
  },
  {
    prop: "desc",
    label: "情况说明",
    width: 120
  },
  {
    prop: "executorAccountList",
    label: "执行人员",
    width: 120,
    render: ({ row }) => {
      return row.executorAccountList?.length ? row.executorAccountList.map(item => item.name).join("、") : "--";
    }
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);
/**
 * 打开 drawer(新增、查看、编辑)
 */
const drawerRef = ref<InstanceType<typeof DetailDrawer> | null>(null);
async function openDrawer(title: "查看" | "新增" | "编辑", row: Partial<WarrantyTask.TaskRes> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: title === "新增" ? addWarrantyTask : title === "编辑" ? editWarrantyTask : undefined,
    getTableList: proTable.value?.getTableList
  };
  if (title !== "新增" && row.id) {
    // const res = await getDeviceDetail(row.id);
    params.row = row;
  }

  drawerRef.value?.acceptParams(params);
}

const router = useRouter();

function goDetail(id: string) {
  router.push({ name: "warrantyTaskFeedback", params: { id } });
}

const assignTask = ref<InstanceType<typeof AssignTasks> | null>(null);
function assignTasks(row: WarrantyTask.TaskRes) {
  assignTask.value?.acceptParams({
    row,
    api: assignExecutor,
    getTableList: proTable.value!.getTableList
  });
}

/**驳回 */
function handleReject(row: WarrantyTask.TaskRes) {
  useHandleData(audit, { id: row.id, operate: 0 }, "驳回");
}
/* 通过 */
function handlePass(row: WarrantyTask.TaskRes) {
  useHandleData(audit, { id: row.id, operate: 1 }, "通过");
}
function handleExport() {
  const searchParam = proTable.value?.searchParam;
  useDownload(exportRepair, `报修任务-${dayjs().format("YYYYMMDD")}`, searchParam);
}
</script>
