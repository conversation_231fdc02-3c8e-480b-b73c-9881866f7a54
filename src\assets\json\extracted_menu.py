import json

# 读取文件
def read_json_file(file_path):
  with open(file_path, 'r') as file:
    data = json.load(file)
    return data

# 提取需要数据
def extract_data(data):
  if isinstance(data, dict):
    result = {}
    if 'name' in data:
      result['name'] = data['name']
    if 'meta' in data and 'title' in data['meta']:
      result['title'] = data['meta']['title']
    if 'path' in data:
      result['path'] = data['path']
    if 'children' in data:
      result['children'] = [extract_data(child) for child in data['children']]
    return result
  elif isinstance(data, list):
    return [extract_data(element) for element in data]
  else:
    return data

# 保存文件
def save_json_file(file_path, data):
  with open(file_path, 'w', encoding='utf-8') as file:
    json.dump(data, file, indent=2, ensure_ascii=False)
    

menuList = read_json_file('./authMenuList.json')
data = extract_data(menuList['data'])
save_json_file('extracted_data.json', data)

print("数据已保存到文件 'extracted_data.json'")
