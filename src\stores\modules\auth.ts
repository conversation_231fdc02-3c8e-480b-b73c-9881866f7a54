import { defineStore } from "pinia";
import { AuthState } from "@/stores/interface";
// import { getAuthButtonListApi, getAuthMenuListApi } from "@/api/modules/login";
import { getFlatMenuList, getShowMenuList, getAllBreadcrumbList } from "@/utils";
import menuList from "@/config/menuList";
import { getAuthMenuListApi } from "@/api/modules/login";

// 定义 b 树节点的类型
interface BTreeNode {
  frontName: string;
  childrenList?: BTreeNode[];
  type?: number;
  // 其他你可能需要的属性
}

// 定义映射存储的节点信息类型
interface NodeMapValue {
  path: string[];
  children: Menu.MenuOptions[];
  node: Menu.MenuOptions;
}

function compareTrees(
  a: Menu.MenuOptions[],
  b: BTreeNode[]
): { authButtonList: Record<string, string[]>; authMenuList: Menu.MenuOptions[] } {
  // 构建映射，键为树 a 中的节点名称，值为该节点的路径和子节点
  const aMap = new Map<string, NodeMapValue>();
  function buildMap(node: Menu.MenuOptions, path: string[] = []) {
    aMap.set(node.name, { path, children: node.children || [], node });
    node.children?.forEach(child => buildMap(child, [...path, node.name]));
  }
  a.forEach(node => buildMap(node));

  // 辅助函数，用于在结果树中根据路径构建节点
  function buildTreeFromPath(path: string[], nodeA: NodeMapValue, result: Menu.MenuOptions[]) {
    let current = result;
    const nodeName = nodeA.node.name;

    const copyNodea = { ...nodeA.node };
    delete copyNodea.children;

    path.forEach(p => {
      let node = current.find(c => c.name === p);
      if (!node) {
        node = { ...copyNodea, children: [] };
        current.push(node);
      }
      current = node.children!;
    });
    if (!current.some(c => c.name === nodeName)) {
      current.push({ ...copyNodea, children: [] });
    }
  }

  let authButtonList: Record<string, string[]> = {};

  // 从树 b 中构建匹配的结构
  function buildMatchingStructure(nodeB: BTreeNode, path: string[] = [], result: Menu.MenuOptions[] = []): Menu.MenuOptions[] {
    let nodeA = aMap.get(nodeB.frontName);
    if (nodeB.type === 2) {
      const pageName = path[path.length - 1];
      if (authButtonList[pageName]) {
        authButtonList[pageName].push(nodeB.frontName);
      } else {
        authButtonList[pageName] = [nodeB.frontName];
      }
    } else if (nodeA) {
      buildTreeFromPath(path, nodeA, result);
      nodeB.childrenList?.forEach(childB => buildMatchingStructure(childB, [...path, nodeB.frontName], result));
    }
    return result;
  }

  // 清理结果中的空子节点数组
  function cleanTree(node: Menu.MenuOptions) {
    if (node.children && node.children.length === 0) {
      delete node.children;
    } else if (node.children) {
      node.children.forEach(child => cleanTree(child));
    }
  }

  let result = b.map(nodeB => buildMatchingStructure(nodeB)).flat();
  result.forEach(node => cleanTree(node));

  return {
    authButtonList,
    authMenuList: result
  };
}

export const useAuthStore = defineStore({
  id: "geeker-auth",
  state: (): AuthState => ({
    // 按钮权限列表
    authButtonList: {},
    // 菜单权限列表
    authMenuList: [],
    // 当前页面的 router name，用来做按钮权限筛选
    routeName: ""
  }),
  getters: {
    // 按钮权限列表
    authButtonListGet: state => state.authButtonList,
    // 菜单权限列表 ==> 这里的菜单没有经过任何处理
    authMenuListGet: state => state.authMenuList,
    // 菜单权限列表 ==> 左侧菜单栏渲染，需要剔除 isHide == true
    showMenuListGet: state => getShowMenuList(state.authMenuList),
    // 菜单权限列表 ==> 扁平化之后的一维数组菜单，主要用来添加动态路由
    flatMenuListGet: state => getFlatMenuList(state.authMenuList),
    // 递归处理后的所有面包屑导航列表
    breadcrumbListGet: state => getAllBreadcrumbList(state.authMenuList)
  },
  actions: {
    // Get AuthButtonList
    // async getAuthButtonList() {
    //   const { data } = await getAuthButtonListApi();
    //   this.authButtonList = data;
    // },
    // Get AuthMenuList
    async getAuthMenuList() {
      const { data: authMenu } = await getAuthMenuListApi();

      const { authButtonList, authMenuList } = compareTrees(menuList, authMenu);
      authMenuList.unshift({
        path: "/home/<USER>",
        name: "home",
        component: "/home/<USER>",
        meta: {
          icon: "HomeFilled",
          title: "首页",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: true,
          isKeepAlive: true
        }
      });
      this.authMenuList = authMenuList;
      this.authButtonList = authButtonList;
    },
    // Set RouteName
    async setRouteName(name: string) {
      this.routeName = name;
    }
  }
});
