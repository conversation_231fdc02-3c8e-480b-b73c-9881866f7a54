import { createApp } from "vue";
import App from "./App.vue";
// reset style sheet
import "@/styles/reset.scss";
// CSS common style sheet
import "@/styles/common.scss";
// iconfont css
import "@/assets/iconfont/iconfont.scss";
// font css
import "@/assets/fonts/font.scss";
// custom element dark css
import "@/styles/element-dark.scss";
// custom element css
import "@/styles/element.scss";
// svg icons
import "virtual:svg-icons-register";
// element icons
import * as Icons from "@element-plus/icons-vue";
// custom directives
import directives from "@/directives/index";
// permission directive
import { permissionDirective } from "@/utils/permission";
// vue Router
import router from "@/routers";
// vue i18n
import I18n from "@/languages/index";
// pinia store
import pinia from "@/stores";
// yup
import { setLocale } from "yup";
import * as yupZh from "@/config/yup-zh";
// errorHandler
import errorHandler from "@/utils/errorHandler";
import "virtual:uno.css";

const app = createApp(App);

app.config.errorHandler = errorHandler;

// register the element Icons component
Object.keys(Icons).forEach(key => {
  app.component(key, Icons[key as keyof typeof Icons]);
});

setLocale(yupZh);

// register permission directive
app.directive('permission', permissionDirective);

app.use(directives).use(router).use(I18n).use(pinia).mount("#app");
