<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getEmergencyLogList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增应急处置日志</el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Edit" @click="openDrawer('编辑', scope.row)">编辑</el-button>
      </template>
    </ProTable>
    <DetailDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="dailyProfitSharing">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { Emergency, getEmergencyLogList, addEmergencyLog, editEmergencyLog } from "@/api/modules/emergency";

import { CirclePlus, Edit } from "@element-plus/icons-vue";
import DetailDrawer from "./components/DetailDrawer.vue";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<Emergency.LogRes>[]>([
  {
    prop: "title",
    label: "标题",
    search: { el: "input" }
  },
  {
    prop: "eventTime",
    label: "事件时间"
  },
  {
    prop: "orgName",
    label: "所属院区"
  },
  {
    prop: "remark",
    label: "备注"
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);
/**
 * 打开 drawer(新增、查看、编辑)
 */
const drawerRef = ref<InstanceType<typeof DetailDrawer> | null>(null);
async function openDrawer(title: "查看" | "新增" | "编辑", row: Partial<Emergency.LogRes> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: title === "新增" ? addEmergencyLog : title === "编辑" ? editEmergencyLog : undefined,
    getTableList: proTable.value?.getTableList
  };
  if (title !== "新增" && row.id) {
    // const res = await getDeviceDetail(row.id);
    params.row = row;
  }

  drawerRef.value?.acceptParams(params);
}
</script>
