<template>
  <el-dialog title="导出" v-model="dialogVisible" width="30%">
    <el-form ref="ruleFormRef" label-width="100px" label-suffix=" :" @submit="onSubmit">
      <el-form-item label="陪护单状态" :error="errors.status">
        <el-select v-bind="formData.status" placeholder="请选择" clearable>
          <el-option v-for="item in WorkerStatusType" :key="item.value" :label="item.label" :value="item.value" clear />
        </el-select>
      </el-form-item>
      <el-form-item label="时间" :error="errors.time">
        <el-date-picker
          v-bind="formData.time"
          type="daterange"
          value-format="YYYYMMDD"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <!-- <el-form-item label="是否有效" :error="errors.versionFlag">
        <el-select v-bind="formData.versionFlag" placeholder="请选择" clearable>
          <el-option v-for="item in versionList" :key="item.key" :label="item.label" :value="item.value" clearable />
        </el-select>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import { toTypedSchema } from "@vee-validate/yup";
import { object, number, array, boolean } from "yup";
import { useForm } from "vee-validate";
import { Nurse, WorkerStatusType, exportNurse } from "@/api/modules/nurseManage";
import { useDownload } from "@/hooks/useDownload";

const dialogVisible = ref(false);

// 有效状态
const versionList = [
  {
    key: 1,
    label: "有效",
    value: true
  },
  {
    key: 2,
    label: "无效",
    value: false
  }
];

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    status: number()
      .transform(value => (isNaN(value) ? undefined : value))
      .label("状态"),
    time: array().nullable().label("时间"),
    versionFlag: boolean()
      .transform(value => (value === "" ? undefined : value))
      .label("有效状态")
  })
);

const { defineComponentBinds, handleSubmit, errors } = useForm<{
  status?: number;
  time?: [string, string];
  versionFlag?: boolean;
}>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  status: defineComponentBinds("status", elPlusConfig),
  time: defineComponentBinds("time", elPlusConfig),
  versionFlag: defineComponentBinds("versionFlag", elPlusConfig)
});

// 接收父组件传过来的参数
const acceptParams = () => {
  dialogVisible.value = true;
};

const onSubmit = handleSubmit(async values => {
  // console.log(values);
  const params: Nurse.ExportNurseParams = {
    status: values.status,
    versionFlag: values.versionFlag
  };
  if (values.time) {
    params.startTime = values.time[0];
    params.endTime = values.time[1];
  }
  useDownload(exportNurse, `排班信息导出-${dayjs().format("YYYYMMDD")}`, params);
});

defineExpose({
  acceptParams
});
</script>
