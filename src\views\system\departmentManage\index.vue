<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      row-key="id"
      :indent="20"
      :columns="columns"
      :pagination="false"
      :request-api="requestApi"
      :init-param="initParam"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增组织</el-button>
        <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除组织
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="View" @click="openDrawer('查看', scope.row)">查看</el-button>
        <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button type="primary" link :icon="Delete" @click="delOrganization(scope.row)">删除</el-button>
        <el-button type="primary" link :icon="CirclePlus" @click="openDrawer('新增', scope.row)">新增下级</el-button>
      </template>
    </ProTable>
    <DepartmentDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="departmentManage">
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import DepartmentDrawer from "./components/DepartmentDrawer.vue";
import { CirclePlus, Delete, EditPen, View } from "@element-plus/icons-vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { editOrganization, addOrganization, deleteOrganization, Org } from "@/api/modules/organization";
import { StatusEnum } from "@/utils/dict";
import { useOrgStore } from "@/stores/modules/orgId";

async function requestApi() {
  const res = await orgStore.refreshOrgTree();
  return {
    data: res
  };
}

// ProTable 实例
const proTable = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ departmentId: "" });

// 表格配置项
const columns = reactive<ColumnProps<Org.ResOrganizationTree>[]>([
  { type: "selection", fixed: "left", width: 70 },
  // { type: "index", label: "#", width: 80 },
  { prop: "name", label: "名称", align: "left" },
  {
    prop: "status",
    label: "状态",
    width: 80,
    render: scope => {
      return (
        <el-switch model-value={scope.row.status} active-value={1} inactive-value={0} onChange={() => changeStatus(scope.row)} />
      );
    }
  },
  { prop: "operation", label: "操作", width: 400, fixed: "right" }
]);

const orgStore = useOrgStore();

// 删除用户信息
const delOrganization = async (params: Org.ResOrganizationTree) => {
  await useHandleData(deleteOrganization, [params.id], `删除【${params.name}】`);
  proTable.value?.getTableList();
  orgStore.refreshOrg();
};

// 批量删除
const batchDelete = async (ids: string[]) => {
  await useHandleData(deleteOrganization, ids, `批量删除`);
  proTable.value?.getTableList();
  orgStore.refreshOrg();
};

/**
 * 禁用启用角色
 * @param row
 */
async function changeStatus(row: Org.ResOrganizationTree) {
  if (row.status === StatusEnum.NO) {
    await useHandleData(editOrganization, { ...row, status: StatusEnum.OFF }, `禁用【${row.name}】组织`);
  } else {
    await useHandleData(editOrganization, { ...row, status: StatusEnum.NO.valueOf() }, `启用【${row.name}】组织`);
  }

  proTable.value?.getTableList();
  orgStore.refreshOrg();
}

// 打开 drawer(新增、查看、编辑)
const drawerRef = ref<InstanceType<typeof DepartmentDrawer> | null>(null);
const openDrawer = (title: string, row: Partial<Org.ReqOrganizationParams> = {}) => {
  let editrow = { ...row };
  if (title === "新增" && row.id) {
    editrow = {
      parentId: row.id
    };
  }

  const params = {
    title,
    row: editrow,
    isView: title === "查看",
    organizationList: proTable.value!.tableData,
    api: title === "新增" ? addOrganization : title === "编辑" ? editOrganization : undefined,
    getTableList: () => {
      proTable.value?.getTableList();
      orgStore.refreshOrg();
    }
  };
  drawerRef.value?.acceptParams(params);
};
</script>
