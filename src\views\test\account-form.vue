<template>
  <div class="main-box">
    <div class="table-box">
      <div class="card table-main">
        <div class="table-header">
          <div class="header-button-lf">
            <h4>账号表单测试 - 医院选择修改为单选</h4>
          </div>
        </div>
        
        <el-form
          ref="ruleFormRef"
          :model="userDetail"
          :rules="rules"
          label-width="120px"
          :validate-on-rule-change="false"
        >
          <el-form-item label="用户姓名" prop="nickname">
            <el-input v-model="userDetail.nickname" placeholder="请填写用户姓名" />
          </el-form-item>
          
          <el-form-item label="电话号码" prop="phoneNumber">
            <el-input v-model="userDetail.phoneNumber" placeholder="请填写电话号" />
          </el-form-item>
          
          <el-form-item label="用户所属医院" prop="hospitalId">
            <el-select
              class="w-full"
              v-model="userDetail.hospitalId"
              placeholder="请选择用户所属医院"
              clearable
            >
              <el-option v-for="item in hospitalList" :key="item.id" :value="String(item.id)" :label="item.name"></el-option>
            </el-select>
            <!-- 新增时显示备注 -->
            <div class="form-tip">
              <el-icon class="tip-icon"><InfoFilled /></el-icon>
              <span class="tip-text">院区绑定后不可修改</span>
            </div>
          </el-form-item>
          
          <el-form-item label="用户所属部门" prop="departmentIds">
            <el-select
              class="w-full"
              v-model="userDetail.departmentIds"
              multiple
              placeholder="请选择用户所属部门"
              :render-after-expand="false"
            >
              <el-option v-for="item in departmentList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="onSubmit">提交测试</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
        
        <div class="mt-4">
          <h5>当前表单数据：</h5>
          <pre>{{ JSON.stringify(userDetail, null, 2) }}</pre>
        </div>
        
        <div class="mt-4">
          <h5>院区数据：</h5>
          <pre>{{ JSON.stringify(hospitalList, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="AccountFormTest">
import { ref, reactive, onMounted, toRef, computed } from "vue";
import { Hospital } from "@/api/interface";
import { getResourceData } from "@/api/modules/hospital";
import { useOrgStore } from "@/stores/modules/orgId";
import type { ElForm, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";

// 控制是否显示验证错误（只有在提交后才显示）
const hasSubmitted = ref(false);

// 动态验证规则，只有在提交后才显示错误
const rules = computed<FormRules>(() => {
  if (!hasSubmitted.value) {
    return {}; // 未提交时，不显示任何验证规则
  }

  return {
    nickname: [{ required: true, message: "请填写用户姓名" }],
    phoneNumber: [{ required: true, message: "请填写电话号" }],
    hospitalId: [{ required: true, message: "请选择用户所属医院" }],
    departmentIds: [
      {
        validator: orgValidator
      }
    ]
  };
});

const userDetail = ref({
  nickname: "",
  phoneNumber: "",
  hospitalId: "",
  departmentIds: []
});

function orgValidator(rule: any, value: string, callback: any) {
  if (userDetail.value.departmentIds && userDetail.value.departmentIds.length !== 0) {
    callback();
  } else {
    callback(new Error("物业部门必须填一个"));
  }
}

/**
 * 院区列表
 */
const hospitalList = ref<Hospital.ResResourceData[]>([]);

/**
 * 部门列表
 */
const orgStore = useOrgStore();
const departmentList = toRef(orgStore, "departmentList");

/**
 * 获取院区数据
 */
async function getHospitalList() {
  try {
    const { data } = await getResourceData();
    hospitalList.value = data;
    console.log("获取到的院区数据:", data);
  } catch (error) {
    console.error("获取院区数据失败:", error);
    ElMessage.error("获取院区数据失败");
  }
}

const ruleFormRef = ref<InstanceType<typeof ElForm>>();

/**
 * 提交表单
 */
function onSubmit() {
  // 标记已经尝试提交，开始显示验证错误
  hasSubmitted.value = true;

  ruleFormRef.value?.validate(async isValid => {
    if (!isValid) {
      return;
    }
    
    const params = { ...userDetail.value };

    // hospitalId 直接使用字符串格式提交
    const submitData = {
      ...params
      // hospitalId 保持字符串格式，不需要转换
    };
    
    console.log("提交的数据:", submitData);
    ElMessage.success("表单验证通过！请查看控制台输出");
  });
}

/**
 * 重置表单
 */
function resetForm() {
  ruleFormRef.value?.resetFields();
  userDetail.value = {
    nickname: "",
    phoneNumber: "",
    hospitalId: "",
    departmentIds: []
  };
}

onMounted(() => {
  getHospitalList();
});
</script>

<style scoped>
.main-box {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.table-box {
  flex: 1;
  overflow: hidden;
  border-radius: 12px;
}

.card {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--el-border-radius-base);
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.el-form {
  width: 100%;
  margin-top: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.form-tip {
  display: flex;
  align-items: center;
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
}

.tip-icon {
  margin-right: 4px;
  font-size: 14px;
}

.tip-text {
  line-height: 1;
}
</style>
