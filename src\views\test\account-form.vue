<template>
  <div class="main-box">
    <div class="table-box">
      <div class="card table-main">
        <div class="table-header">
          <div class="header-button-lf">
            <h4>账号表单测试 - 医院选择修改为单选</h4>
          </div>
        </div>
        
        <el-form ref="ruleFormRef" :model="userDetail" :rules="rules" label-width="120px">
          <el-form-item label="用户姓名" prop="nickname">
            <el-input v-model="userDetail.nickname" placeholder="请填写用户姓名" />
          </el-form-item>
          
          <el-form-item label="电话号码" prop="phoneNumber">
            <el-input v-model="userDetail.phoneNumber" placeholder="请填写电话号" />
          </el-form-item>
          
          <el-form-item label="用户所属医院" prop="hospitalId">
            <el-select
              class="w-full"
              v-model="userDetail.hospitalId"
              placeholder="请选择用户所属医院"
              clearable
            >
              <el-option v-for="item in hospitalList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="用户所属部门" prop="departmentIds">
            <el-select
              class="w-full"
              v-model="userDetail.departmentIds"
              multiple
              placeholder="请选择用户所属部门"
              :render-after-expand="false"
            >
              <el-option v-for="item in departmentList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="onSubmit">提交测试</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
        
        <div class="mt-4">
          <h5>当前表单数据：</h5>
          <pre>{{ JSON.stringify(userDetail, null, 2) }}</pre>
        </div>
        
        <div class="mt-4">
          <h5>院区数据：</h5>
          <pre>{{ JSON.stringify(hospitalList, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="AccountFormTest">
import { ref, reactive, onMounted, toRef } from "vue";
import { Hospital } from "@/api/interface";
import { getResourceData } from "@/api/modules/hospital";
import { useOrgStore } from "@/stores/modules/orgId";
import type { ElForm, FormRules } from "element-plus";
import { ElMessage } from "element-plus";

const rules = reactive<FormRules>({
  nickname: [{ required: true, message: "请填写用户姓名" }],
  phoneNumber: [{ required: true, message: "请填写电话号" }],
  hospitalId: [{ required: true, message: "请选择用户所属医院", trigger: "change" }],
  departmentIds: [
    {
      validator: orgValidator,
      trigger: "blur"
    }
  ]
});

const userDetail = ref({
  nickname: "",
  phoneNumber: "",
  hospitalId: "",
  departmentIds: []
});

function orgValidator(rule: any, value: string, callback: any) {
  if (userDetail.value.departmentIds && userDetail.value.departmentIds.length !== 0) {
    callback();
  } else {
    callback(new Error("物业部门必须填一个"));
  }
}

/**
 * 院区列表
 */
const hospitalList = ref<Hospital.ResResourceData[]>([]);

/**
 * 部门列表
 */
const orgStore = useOrgStore();
const departmentList = toRef(orgStore, "departmentList");

/**
 * 获取院区数据
 */
async function getHospitalList() {
  try {
    const { data } = await getResourceData();
    hospitalList.value = data;
    console.log("获取到的院区数据:", data);
  } catch (error) {
    console.error("获取院区数据失败:", error);
    ElMessage.error("获取院区数据失败");
  }
}

const ruleFormRef = ref<InstanceType<typeof ElForm>>();

/**
 * 提交表单
 */
function onSubmit() {
  ruleFormRef.value?.validate(async isValid => {
    if (!isValid) {
      return;
    }
    
    const params = { ...userDetail.value };

    // hospitalId 直接使用字符串格式提交
    const submitData = {
      ...params
      // hospitalId 保持字符串格式，不需要转换
    };
    
    console.log("提交的数据:", submitData);
    ElMessage.success("表单验证通过！请查看控制台输出");
  });
}

/**
 * 重置表单
 */
function resetForm() {
  ruleFormRef.value?.resetFields();
  userDetail.value = {
    nickname: "",
    phoneNumber: "",
    hospitalId: "",
    departmentIds: []
  };
}

onMounted(() => {
  getHospitalList();
});
</script>

<style scoped>
.main-box {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.table-box {
  flex: 1;
  overflow: hidden;
  border-radius: 12px;
}

.card {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--el-border-radius-base);
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.el-form {
  width: 100%;
  margin-top: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
