import { ReqPage, ResPage } from "@/api/interface/index";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

export namespace Bill {
  /**
   * 账单类型
   */
  export enum BillType {
    /**
     * 未知
     */
    Unknown = -1,
    /**
     * 周期账单
     */
    PeriodicBill = 1,
    /**
     * 预付单
     */
    Prepayment = 2,
    /**
     * 补偿账单
     */
    CompensationBill = 3,
    /**
     * 结算单
     */
    SettlementBill = 11
  }

  /**
   * 周期账单子类型
   */
  export enum BillSubType {
    /**
     * 未知
     */
    Unknown = -1,
    /**
     * 子账单默认类型
     */
    Default = 0,
    /**
     * 月度
     */
    Monthly = 11,
    /**
     * 季度
     */
    Quarterly = 12,
    /**
     * 年度
     */
    Annually = 13
  }
  /**
   * 支付状态枚举
   */
  export enum PayStatus {
    /** 已过期 */
    Expired = -4,
    /** 支付失败 */
    Failure = -3,
    /** 取消 */
    Cancelled = -2,
    /** 未知 */
    Unknown = -1,
    /** 未支付 */
    NotPay = 0,
    /** 待支付 */
    Pending = 1,
    /** 支付成功 */
    Success = 2,
    /** 订单已关闭 */
    Closed = 3,
    /** 全部退款 */
    RefundAll = 10,
    /** 部分退款 */
    RefundPartial = 11
  }

  /**
   * 交易类型枚举
   */
  export enum TradeType {
    /** 未知 */
    Unknown = -1,
    /** 待定 */
    Tentative = 0,
    /** 现金 */
    Cash = 1,
    /** 微信 */
    WeChat = 2
  }
  /** 付费凭证 */
  export enum NeedCertificate {
    NO = 0,
    YES = 1
  }
  /**发票类型 */
  export enum InvoiceType {
    Receipt = 1,
    Invoice = 2,
    PhysicalReceipt = 3
  }

  /** */
  export interface BillProperties {
    /** 账单ID */
    id: string;
    /** 订单ID */
    orderId: number;
    /** 账单类型 */
    billType: BillType;
    /** 账单子类型 */
    billSubType: BillSubType;
    /** 总共应收款金额，单位:分 */
    priceReceivable: number;
    /** 已收金额，单位:分 */
    priceReceived: number;
    /** 账单金额，单位:分 */
    price: number;
    /** 总共应退款金额，单位:分 */
    priceRefundable: number;
    /** 已退款金额，单位:分 */
    priceRefunded: number;
    /** 支付状态 */
    payStatus: PayStatus;
    /** 账单包含套餐名称 */
    itemNames: string;
    /** 备注,账单说明 */
    remark: string;
    /** 折扣 */
    discount: number;
    /** 账单支付时间 */
    payTime: string;
    /** 交易类型 */
    tradeType: TradeType;
    /** 账单创建时间 */
    createTime: string;
    /** 账单开始时间 */
    startTime: string;
    /** 账单结束时间 */
    endTime: string;
    /** 病人姓名 */
    patientName: string;
    /** 护工列表*/
    nursingList: Array<{
      id: string;
      name: string;
    }>;
    /**发票信息 */
    invoice: {
      id: string;
      orderId: string;
      /**是否需要付费凭证,0 不需要，1 需要	 */
      needCertificate: NeedCertificate;
      /**类型: 1 电子收据 2 电子发票 3 实体收据	 */
      invoiceType: InvoiceType;
      /**电子邮箱 */
      email: string;
      /**纳税人名称	 */
      taxpayerName: string;
      /**纳税人识别号	*/
      taxpayerNo: string;
    };
  }

  /** 消费详情 */
  export interface ConsumerLogProperties {
    /**
     * 主键ID
     */
    id: number;

    /**
     * 日期, 格式yyyyMMdd
     */
    date: number;

    /**
     * 订单ID
     */
    orderId: number;

    /**
     * 关联套餐
     */
    itemId: number;

    /**
     * 关联套餐名称
     */
    itemName: string;

    /**
     * 总金额：分
     */
    totalPrice: number;

    /**
     * 物业分成比例
     */
    rateCertifiedProperty: number;

    /**
     * 物业金额
     */
    priceCertifiedProperty: number;

    /**
     * 医院分成比例
     */
    rateHospital: number;

    /**
     * 医院金额
     */
    priceHospital: number;

    /**
     * 护工分成比例
     */
    rateNursing: number;

    /**
     * 护工金额
     */
    priceNursing: number;

    /**
     * 版本，有效版本 1 其他无效版本
     */
    version: string;

    /**
     * 护工列表
     */
    nursingList: Array<{
      nursingId: string;
      nursingName: string;
      rateNursing: number;
      priceNursing: number;
    }>;
  }

  export interface queryBillParams {
    orderId: string;
    createTime: [string, string];
    tradeType: TradeType;
    /**是否筛选退款账单: null 0 否 1 是 */
    filterRefundBill: number;
  }
}

export const billTypeOptions = [
  // { value: CompanionshipOrder.BillType.Unknown, label: "未知" },
  { value: Bill.BillType.PeriodicBill, label: "周期账单" },
  { value: Bill.BillType.Prepayment, label: "预付单" },
  // { value: CompanionshipOrder.BillType.CompensationBill, label: "补偿账单" },
  { value: Bill.BillType.SettlementBill, label: "结算单" }
];

export const billSubTypeOptions = [
  // { value: CompanionshipOrder.BillSubType.Unknown, label: "未知" },
  // { value: CompanionshipOrder.BillSubType.Default, label: "子账单默认类型" },
  { value: Bill.BillSubType.Monthly, label: "月度" },
  { value: Bill.BillSubType.Quarterly, label: "季度" },
  { value: Bill.BillSubType.Annually, label: "年度" }
];

export const tradeTypeOptions = [
  // { value: Bill.TradeType.Unknown, label: "未知" },
  { value: Bill.TradeType.Tentative, label: "待定" },
  { value: Bill.TradeType.Cash, label: "现金" },
  { value: Bill.TradeType.WeChat, label: "微信" }
];

export const payStatusOptions = [
  { value: Bill.PayStatus.Expired, label: "已过期" },
  { value: Bill.PayStatus.Failure, label: "支付失败" },
  { value: Bill.PayStatus.Cancelled, label: "取消" },
  { value: Bill.PayStatus.Unknown, label: "未知" },
  { value: Bill.PayStatus.NotPay, label: "未支付" },
  { value: Bill.PayStatus.Pending, label: "待支付" },
  { value: Bill.PayStatus.Success, label: "支付成功" },
  { value: Bill.PayStatus.Closed, label: "订单已关闭" },
  { value: Bill.PayStatus.RefundAll, label: "全部退款" },
  { value: Bill.PayStatus.RefundPartial, label: "部分退款" }
];

export const consumerLogVersion = [
  { value: "1", label: "有效" },
  { value: "0", label: "无效" }
];

export const needCertificateOptions = [
  { value: Bill.NeedCertificate.NO, label: "不需要" },
  { value: Bill.NeedCertificate.YES, label: "需要" }
];

export const invoiceTypeOptions = [
  { value: Bill.InvoiceType.Receipt, label: "电子收据" },
  { value: Bill.InvoiceType.Invoice, label: "电子发票" },
  { value: Bill.InvoiceType.PhysicalReceipt, label: "实体收据" }
];

export const filterRefundBillOptions = [
  { value: 0, label: "否" },
  { value: 1, label: "是" }
];

/**
 * @name 账单列表
 */
export const getBillListApi = (params: Partial<Bill.queryBillParams> & ReqPage) => {
  return http.post<ResPage<Bill.BillProperties>>(PORT1 + `/order/bill/paging`, params, { loading: true });
};

/**
 * @name 账单数据统计
 */
export const getBillStatistics = (params: Partial<Bill.queryBillParams> & ReqPage) => {
  return http.post<{ priceReceived: string; priceUnReceived: string }>(PORT1 + `/order/bill/statistics`, params, {
    loading: true
  });
};

/**
 * @name 设置为现金支付
 *  * @cashSource 1 现金 3 支付宝
 */
export const cashPay = (params: { billId: string; cashSource?: number }) => {
  return http.post(PORT1 + `/order/bill/cashPay`, params, { loading: true });
};

/**
 * @name 账单消费明细
 */
export const getBillDetailApi = (params: { orderId?: string; startTime?: string; endTime?: string } & ReqPage) => {
  return http.post(PORT1 + `/order/consumerLog/paging`, params, { loading: true });
};

/**
 * @name 导出账单列表
 */
export const exportBillDate = (params: Partial<Bill.queryBillParams>) => {
  return http.download(PORT1 + `/order/bill/export`, params, { loading: true });
};

/**
 * @name 导出每日分成
 */
export const exportBillDetail = (params: { orderId?: string; startTime?: string; endTime?: string }) => {
  return http.download(PORT1 + `/order/consumerLog/export`, params, { loading: true });
};
