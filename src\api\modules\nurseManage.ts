import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";
import { Gender } from "@/utils/dict";

export namespace Nurse {
  export enum SettleWay {
    /**
     * 未知
     */
    Unknown = -1,
    /**
     * 月结
     */
    Monthly = 1,
    /**
     * 一次性结算
     */
    OneTime = 2
  }

  export enum NursingStatus {
    /**
     * 未知
     */
    Unknown = -1,
    /**
     * 审核中
     */
    UnderReview = 0,
    /**
     * 生效
     */
    Active = 1,
    /**
     * 失效
     */
    Inactive = 2
  }

  export enum WorkerStatus {
    /**
     * 请假
     */
    Leave = 0,
    /**
     * 空闲
     */
    Free = 1,
    /**
     * 工作中
     */
    Working = 2
  }

  export interface ResNurse {
    /**
     * ID
     */
    id: string;
    /**
     * 所属院区
     */
    hospitalList: { name: string; id: string }[];
    /**
     * 姓名
     */
    name: string;
    /**
     * 性别
     * -1 : 未知
     * 0 : 女
     * 1 : 男
     */
    gender: Gender;
    /**
     * 年龄
     */
    age: number;
    /**
     * 服务年限
     */
    workYear: number;
    /**
     * 特长介绍
     */
    specialty?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 费用结算方式
     * -1 : 未知
     * 1 : 按月结算
     * 2 : 一次性结算
     */
    settleWay: SettleWay;
    /**
     * 费用结算时间（工作满x月后结算）
     */
    settleTime: string;
    /**
     * 介绍人
     */
    sponsor?: string;
    /**
     * 介绍费金额,单位分
     */
    sponsorPrice?: number;
    /**
     * 证件照
     */
    idCardPic?: string;
    /**
     * 资质与证明
     */
    certificationPicList?: string[];
    /**
     * 状态
     * 0 : 审核中
     * 1 : 生效
     * 2 : 失效
     */
    status: NursingStatus;
  }
  export interface ParamsNurse {
    id?: string;
    /**
     * 所属院区
     */
    orgIds: string[];
    /**
     * 姓名
     */
    name: string;
    /**
     * 性别
     * -1 : 未知
     * 0 : 女
     * 1 : 男
     */
    gender?: Gender;
    /**
     * 年龄
     */
    age?: number;
    /**
     * 服务年限
     */
    workYear?: number;
    /**
     * 特长介绍
     */
    specialty?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 费用结算方式
     * -1 : 未知
     * 1 : 按月结算
     * 2 : 一次性结算
     */
    settleWay?: SettleWay;
    /**
     * 费用结算时间（工作满x月后结算）
     */
    settleTime?: string;
    /**
     * 介绍人
     */
    sponsor?: string;
    /**
     * 介绍费金额,单位分
     */
    sponsorPrice?: number;
    /**
     * 星级
     */
    nursingStar?: number;
    /**
     * 证件照
     */
    idCardPic?: string;
    /**
     * 资质与证明
     */
    certificationPicList?: string[];
    /**
     * 状态
     * 0 : 审核中
     * 1 : 生效
     * 2 : 失效
     */
    status?: NursingStatus;
    /**
     *  联系方式
     */
    mobile?: string;
  }

  /**
   * @name 护工排班信息
   */
  export interface ResNurseSchedule {
    id: string;
    date: number;
    nursingId: number;
    nursingName: string;
    status: WorkerStatus;
    orderList?: {
      id: string;
      orderId: string;
      orderName: string;
      orderDate: number;
    }[];
  }
  /**
   * @name 护工排班查询
   */
  export interface ParamsNurseSchedule {
    /**
     * 陪护单号
     */
    orderId: string;
  }
  /**
   * @name 护工排班导出
   */
  export interface ExportNurseParams {
    /**
     * 状态
     */
    status?: WorkerStatus;
    /**
     * 开始时间
     */
    startTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 是否有效
     */
    versionFlag?: boolean;
  }
}

/**
 * @description：费用结算方式
 */
export const settleWayType = [
  { label: "按月结算", value: Nurse.SettleWay.Monthly },
  { label: "一次性结算", value: Nurse.SettleWay.OneTime }
];

/**
 * 排班状态
 */
export const WorkerStatusType = [
  { label: "请假", value: Nurse.WorkerStatus.Leave },
  { label: "空闲", value: Nurse.WorkerStatus.Free },
  { label: "忙", value: Nurse.WorkerStatus.Working }
];
/**
 * @name 护工列表
 */
export const getNurseList = (params: ReqPage & { name?: string; status?: Nurse.NursingStatus }, loading = true) => {
  return http.get<ResPage<Nurse.ResNurse>>(PORT1 + `/nursing/paging`, params, { loading });
};

/**
 * @name 护工详情
 */
export const getNurseDetail = (id: string) => {
  return http.get<Nurse.ResNurse>(PORT1 + `/nursing/${id}`, { loading: true });
};

/**
 * @name 新增护工
 */

export const addNurse = (params: Nurse.ParamsNurse) => {
  return http.post(PORT1 + `/nursing`, params, { loading: true });
};

/**
 * @name 编辑护工
 */
export const editNurse = (params: Partial<Nurse.ParamsNurse>) => {
  return http.put(PORT1 + `/nursing`, params, { loading: true });
};

/**
 * @name 删除护工
 */
export const deleteNurse = (id: string) => {
  return http.delete(PORT1 + `/nursing/${id}`, {}, { loading: true });
};

/**
 * @name 获取排班信息
 */
export const getNurseSchedule = (params: Partial<Nurse.ParamsNurseSchedule>) => {
  return http.get<ResPage<Nurse.ResNurseSchedule>>(PORT1 + `/nursing/schedule/paging`, params, { loading: true });
};

/**
 * @name 请假
 */
export const leaveNurse = (params: { id: string }) => {
  return http.post(PORT1 + `/nursing/schedule/leave`, params, { loading: true });
};

/**
 * @name 取消请假
 */
export const cancelLeaveNurse = (params: { id: string }) => {
  return http.post(PORT1 + `/nursing/schedule/cancel-leave`, params, { loading: true });
};

/**
 * @name 设置替班
 */
export const setNurseSchedule = (params: { id: string; nursingId: string; nursingName: string }) => {
  return http.post(PORT1 + `/nursing/schedule/substitute`, params, { loading: true });
};

/**
 * @name 导出排班
 */
export const exportNurse = (params: Nurse.ExportNurseParams) => {
  return http.download(PORT1 + `/nursing/schedule/export`, params, { loading: true });
};
