import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";
import { Device } from "./device";

export namespace WarrantyTask {
  export enum Status {
    /* 未指派 */
    Unassigned = 1,
    /* 已指派 */
    Assigned = 2,
    /* 未完成 */
    Uncompleted = 3,
    /* 待审核 */
    PendingReview = 4,
    /* 已完成 */
    Completed = 5
  }

  export enum SourceType {
    /* PC */
    PC = 1,
    /* H5 */
    H5 = 2
  }

  export enum BizType {
    /* 巡检计划任务 */
    InspectionPlanTask = 1,
    /* 维保计划任务 */
    MaintenancePlanTask = 2
  }

  export enum UrgencyDegree {
    /* 一般 */
    Normal = 0,
    /* 紧急 */
    Urgent = 1
  }

  export interface TaskRes {
    /* 主键ID */
    id: string;
    /* 编号 */
    code: string;
    /* 报修系统类型 */
    systemType: Device.SystemType;
    /* 状态 */
    status: Status;
    /* 创建人员 */
    createBy: number;
    /* 创建名称 */
    createByName: string;
    /* 报修时间 */
    createAt: string;
    /* 备注 */
    remark: string;
    /* 设备ID */
    deviceId: number;
    /* 设备名称 */
    deviceName: string;
    /* 附件URL,数组 */
    attachments: string;
    /* 来源 */
    sourceType: SourceType;
    /* 关联业务ID */
    bizId: number;
    /* 关联业务 */
    bizType: BizType;
    /* 完成时间 */
    completedTime: string;
    /* 派工人员ID */
    assignerAccountId: number;
    /* 派工人员名称 */
    assignerAccountName: string;
    /** 紧急程度 */
    urgencyDegreeType: WarrantyTask.UrgencyDegree;
    /* 执行人员列表 */
    executorAccountList: {
      /* 执行人员ID */
      id: number;
      /* 执行人员名称 */
      name: string;
    }[];
  }

  export interface TaskParams {
    systemType: Device.SystemType;
    /* 设备ID */
    deviceId: number;
    /* 附件URL,数组 */
    attachments: string;
    /* 来源 */
    sourceType: SourceType;
    /* 关联业务ID */
    bizId: number;
    /* 关联业务 */
    bizType: BizType;
    /* 备注 */
    remark: string;
    /* 报修位置 */
    location: string;
  }

  export interface PartInfo {
    /* 反馈ID */
    feedbackId: number;
    /* 备件批次ID */
    partBatchId: number;
    /* 备件数量 */
    quantity: number;
    /* 备件名称 */
    name: string;
  }
  export enum FeedbackType {
    /** 完成任务 */
    CompleteTask = 1,
    /** 需协助 */
    NeedAssistance = 2,
    /** 需要使用备件 */
    NeedSpareParts = 3
  }

  export interface FeedbackRes {
    /* 报修单编号 */
    code: string;
    /* 报修系统类型 */
    systemType: Device.SystemType;
    /* 反馈人员ID */
    createBy: number;
    /* 反馈人员名称 */
    createByName: string;
    /* 反馈时间 */
    createAt: string;
    /* 反馈结果类型 */
    feedbackType: FeedbackType;
    /* 情况说明 */
    remark: string;
    /* 附件URL,数组 */
    attachments: string;
    /* 备件信息 */
    part: PartInfo[];
  }

  export enum ApprovalStatus {
    /* 待审核 */
    Pending = 1,
    /* 允许使用 */
    Approved = 2,
    /* 不允许使用 */
    Denied = 3
  }

  interface RepairFormFeedbackInfo {
    /* 报修单反馈ID */
    id: number;
    /* 报修单反馈人员ID */
    createBy: number;
    /* 报修单反馈人员名称 */
    createByName: number;
    /* 报修单反馈时间 */
    createAt: string;
    /* 反馈结果类型 */
    type: FeedbackType;
    /* 反馈情况说明 */
    remark?: string;
    /* 附件URL,数组 */
    attachments?: string; // If this is indeed an array, use string[]
  }

  export interface ApprovalRes {
    /* 审批ID */
    id: number;
    /* 审批单编号 */
    code: string;
    /* 状态 */
    status: ApprovalStatus;
    /* 反馈信息 */
    repairFormFeedbackInfo: RepairFormFeedbackInfo;
    /* 备件信息 */
    partInfo?: PartInfo; // Optional since it's not in the 'required' array
    /* 审核结果说明 */
    auditRemark?: string;
  }
}

export const statusOptions = [
  /* 未指派 */
  { value: WarrantyTask.Status.Unassigned, label: "未指派" },
  /* 已指派 */
  { value: WarrantyTask.Status.Assigned, label: "已指派" },
  /* 未完成 */
  { value: WarrantyTask.Status.Uncompleted, label: "未完成" },
  /* 待审核 */
  { value: WarrantyTask.Status.PendingReview, label: "待审核" },
  /* 已完成 */
  { value: WarrantyTask.Status.Completed, label: "已完成" }
];

export const feedbackTypeOptions = [
  /* 完成任务 */
  { value: WarrantyTask.FeedbackType.CompleteTask, label: "完成任务" },
  /* 需协助 */
  { value: WarrantyTask.FeedbackType.NeedAssistance, label: "需协助" },
  /* 需要使用备件 */
  { value: WarrantyTask.FeedbackType.NeedSpareParts, label: "需要使用备件" }
];

export const approvalStatusOptions = [
  { value: WarrantyTask.ApprovalStatus.Pending, label: "待审核" },
  { value: WarrantyTask.ApprovalStatus.Approved, label: "允许使用" },
  { value: WarrantyTask.ApprovalStatus.Denied, label: "不允许使用" }
];

export const sourceTypeOptions = [
  { value: WarrantyTask.SourceType.PC, label: "PC" },
  { value: WarrantyTask.SourceType.H5, label: "H5" }
];

export const bizTypeOptions = [
  { value: WarrantyTask.BizType.InspectionPlanTask, label: "巡检计划任务" },
  { value: WarrantyTask.BizType.MaintenancePlanTask, label: "维保计划任务" }
];

export const urgencyDegreeOptions = [
  { value: WarrantyTask.UrgencyDegree.Normal, label: "非紧急" },
  { value: WarrantyTask.UrgencyDegree.Urgent, label: "紧急" }
];

/** 报修单列表 */
export function getWarrantyTaskList(params: ReqPage & { title: string }) {
  return http.post<ResPage<WarrantyTask.TaskRes>>(PORT1 + "/repair/paging", params, { loading: true });
}

/** 新增报修单 */
export function addWarrantyTask(params: Partial<WarrantyTask.TaskParams>) {
  return http.post(PORT1 + "/repair/create", params, { loading: true });
}

/** 修改 */
export function editWarrantyTask(params: Partial<WarrantyTask.TaskParams> & { id: number }) {
  return http.post(PORT1 + "/repair/modify", params, { loading: true });
}

/** 报修单反馈信息 */
export function getWarrantyTaskFeedback(params: any) {
  return http.post<WarrantyTask.TaskRes>(PORT1 + "/repair-feedback/paging", params, { loading: true });
}

/** 报修单审核列表 */
export function getAuditList(params: ReqPage & { title: string }) {
  return http.post<ResPage<WarrantyTask.ApprovalRes>>(PORT1 + "/part-apply/paging", params, { loading: true });
}

/**
 * 通过审核
 */
export function auditPass(params: { id: string; auditRemark?: string }) {
  return http.post(PORT1 + "/part-apply/pass", params, { loading: true });
}

/** 拒绝 */
export function auditReject(params: { id: string; auditRemark?: string }) {
  return http.post(PORT1 + "/part-apply/reject", params, { loading: true });
}

/**
 * @name 分派执行人员
 */
export const assignExecutor = (params: { id: string; executorAccountIdList: string[] }, loading = true) => {
  return http.post(PORT1 + `/repair/assign-executor`, params, { loading });
};

/**
 * 报修单审核
 */
export const audit = (params: { id: string; operate: number; auditRemark?: string }, loading = false) => {
  return http.post(PORT1 + `/repair/auth`, params, { loading });
};

/** 保修任务管理 */
export const exportRepair = (params: any) => {
  return http.download(PORT1 + `/repair/export`, params, { loading: true });
};
