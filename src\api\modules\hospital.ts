import { ReqPage, ResPage, Hospital } from "@/api/interface/index";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

/**
 * @name 院区管理模块
 */

// 获取院区列表
export const getHospitalList = (params: ReqPage & Partial<Hospital.ReqHospitalParams>, loading = true) => {
  return http.get<ResPage<Hospital.ResHospitalList>>(PORT1 + `/hospital/paging`, params, { loading });
};

// 获取院区详情
export const getHospitalDetail = (id: string) => {
  return http.get<Hospital.ResHospitalList>(PORT1 + `/hospital/${id}`);
};

// 新增院区
export const addHospital = (params: Hospital.ReqHospitalParams) => {
  return http.post<{ hospitalId: string }>(PORT1 + `/hospital`, params);
};

// 编辑院区
export const editHospital = (params: Hospital.ReqHospitalParams) => {
  return http.put(PORT1 + `/hospital`, params);
};

// 删除院区
export const deleteHospital = (ids: string[]) => {
  return http.put(PORT1 + `/hospital/delete`, { ids });
};

// 切换院区状态
export const changeHospitalStatus = (params: { id: string; enable: number }) => {
  return http.put(PORT1 + `/hospital/status`, params);
};

// 获取所有启用的院区列表（用于下拉选择）
export const getResourceData = () => {
  return http.get<Hospital.ResResourceData[]>(PORT1 + `/hospital/resourceData`);
};

// 导出院区数据
export const exportHospitalInfo = (params: Hospital.ReqHospitalParams) => {
  return http.download(PORT1 + `/hospital/export`, params);
};

// 批量导入院区
export const batchImportHospital = (params: FormData) => {
  return http.post(PORT1 + `/hospital/import`, params);
};
