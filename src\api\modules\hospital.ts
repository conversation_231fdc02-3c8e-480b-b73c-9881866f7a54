import { ReqPage, ResPage } from "@/api/interface/index";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

/**
 * @name 院区管理模块
 */
export namespace Hospital {
  export interface ReqHospitalParams {
    id?: string;
    name: string;
    address: string;
    remark?: string;
    enable: number;
  }

  export interface ResHospitalList {
    id: string;
    name: string;
    address: string;
    remark: string;
    enable: number;
    deleted: number;
    createBy: string;
    createdAt: string;
    updateBy: string;
    updatedAt: string;
  }
}

// 获取院区列表
export const getHospitalList = (params: ReqPage & Partial<Hospital.ResHospitalList>, loading = true) => {
  return http.get<ResPage<Hospital.ResHospitalList>>(PORT1 + `/hospital/paging`, params, { loading });
};

// 获取院区详情
export const getHospitalDetail = (id: string) => {
  return http.get<Hospital.ResHospitalList>(PORT1 + `/hospital/${id}`);
};

// 新增院区
export const addHospital = (params: Hospital.ReqHospitalParams) => {
  return http.post<{ hospitalId: string }>(PORT1 + `/hospital`, params);
};

// 编辑院区
export const editHospital = (params: Hospital.ReqHospitalParams) => {
  return http.put(PORT1 + `/hospital`, params);
};

// 删除院区
export const deleteHospital = (ids: string[]) => {
  return http.put(PORT1 + `/hospital/delete`, { ids });
};

// 切换院区状态
export const changeHospitalStatus = (params: { id: string; enable: number }) => {
  return http.put(PORT1 + `/hospital/status`, params);
}; 