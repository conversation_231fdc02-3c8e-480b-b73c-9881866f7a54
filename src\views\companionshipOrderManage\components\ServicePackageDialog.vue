<template>
  <el-dialog title="请选择服务套餐" v-model="dialogVisible">
    <el-form ref="ruleFormRef" label-width="100px" label-suffix=" :" @submit="onSubmit">
      <el-form-item :error="errors.itemList" label="服务套餐" required>
        <el-select
          class="w-full"
          v-bind="formData.itemList"
          reserve-keyword
          multiple
          remote
          filterable
          value-key="value"
          :remote-method="remoteMethod"
          :loading="isLoading"
        >
          <el-option v-for="item in options" :key="item.value" :value="item" :label="item.label" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item :error="errors.time" label="生效时间" required>
        <el-date-picker
          type="datetimerange"
          v-bind="formData.time"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="defaultTime"
          format="YYYY-MM-DD HH 时"
          time-format="HH 时"
          value-format="x"
        />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useCompanionshipOrderStore, DialogType } from "@/stores/modules/companionshipOrder";
import { updatePackage } from "@/api/modules/companionshipOrder";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string, array } from "yup";
import { useForm } from "vee-validate";
import { getServerPackageList } from "@/api/modules/servicePackage";
// const defaultTime = new Date(2000, 1, 1, 12, 0, 0);
const companionshipOrderStore = useCompanionshipOrderStore();

interface SelectOption {
  value: string;
  label: string;
}

const {
  state: options,
  isLoading,
  execute: refreshoptions
} = useAsyncState<SelectOption[], [string?]>(
  async query => {
    const { data } = await getServerPackageList({ name: query, pageIndex: 1, pageSize: 20 }, false);
    return data.records.map((item: any) => ({ value: item.id, label: item.name }));
  },
  [],
  { immediate: false }
);

const remoteMethod = (query?: string) => {
  refreshoptions(300, query);
};

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    itemList: array()
      .required()
      .min(1, "请选择服务套餐")
      .of(object({ value: string(), label: string() }))
      .label("服务套餐")
    // time: array().required().length(2).of(number()).label("生效时间")
  })
);

const { defineComponentBinds, handleSubmit, resetForm, errors, setValues } = useForm<{
  itemList: Array<SelectOption>;
  time: [number, number];
}>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  itemList: defineComponentBinds("itemList", elPlusConfig),
  time: defineComponentBinds("time", elPlusConfig)
});

let orderId: string;
const DIALOGTYPE = DialogType.ServerPackage;
const dialogVisible = computed<boolean>({
  set(val) {
    if (val) {
      companionshipOrderStore.dialogInfo.dialogVisible = DIALOGTYPE;
    } else {
      companionshipOrderStore.dialogInfo.dialogVisible = DialogType.None;
    }
    resetForm();
  },
  get() {
    // 显示
    if (companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE) {
      refreshoptions();
      orderId = companionshipOrderStore.dialogInfo.info!.id;
      const itemIdList = companionshipOrderStore.dialogInfo.info?.itemIdList;
      const itemNameList = companionshipOrderStore.dialogInfo.info?.itemNameList;
      debugger;
      if (itemIdList && itemIdList.length > 0) {
        setValues({
          itemList: companionshipOrderStore.dialogInfo.info!.itemIdList.map((item: any, index: number) => ({
            value: item,
            label: itemNameList[index]
          }))
        });
      }
    }
    return companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE;
  }
});

const onSubmit = handleSubmit(async values => {
  console.log(values);
  const params = {
    orderId: orderId,
    itemIdList: values.itemList.map(item => item.value)
  };
  await updatePackage(params);
  ElMessage("修改成功");
  companionshipOrderStore.dialogInfo = {
    info: undefined,
    dialogVisible: DialogType.None
  };
  companionshipOrderStore.getTableList!();
});
</script>
