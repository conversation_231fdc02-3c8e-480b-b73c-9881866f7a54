<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getPlanList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">增加计划</el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Edit" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button v-show="scope.row.status == 1" type="primary" link @click="cancelPlanPatrol(scope.row.id)">作废</el-button>
      </template>
    </ProTable>
    <DetailDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="dailyProfitSharing">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import {
  Plan,
  circleTypeOptions,
  statusOptions,
  getPlanPatrolList,
  editPlanPatrol,
  addPlanPatrol,
  getPlanPatrolDetail,
  deletePlanPatrol,
  getPlanMaintenanceList,
  editPlanMaintenance,
  addPlanMaintenance,
  getPlanMaintenanceDetail,
  deletePlanMaintenance
} from "@/api/modules/plan";
import { CirclePlus, Edit } from "@element-plus/icons-vue";
import DetailDrawer from "./components/DetailDrawer.vue";
import dayjs from "dayjs";
import { useHandleData } from "@/hooks/useHandleData";

const route = useRoute();
let getPlanList: typeof getPlanPatrolList,
  addPlan: typeof addPlanPatrol,
  editPlan: typeof editPlanPatrol,
  getPlanDetail: typeof getPlanPatrolDetail,
  deletePlan: typeof deletePlanPatrol;
let title = "";
if (route.name === "planPatrol") {
  title = "巡检计划";
  getPlanList = getPlanPatrolList;
  addPlan = addPlanPatrol;
  editPlan = editPlanPatrol;
  getPlanDetail = getPlanPatrolDetail;
  deletePlan = deletePlanPatrol;
} else {
  title = "维保计划";
  getPlanList = getPlanMaintenanceList;
  addPlan = addPlanMaintenance;
  editPlan = editPlanMaintenance;
  getPlanDetail = getPlanMaintenanceDetail;
  deletePlan = deletePlanMaintenance;
}

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<Plan.PlanRes>[]>([
  {
    prop: "code",
    label: `${title}编号`,
    width: 200,
    search: { el: "input" }
  },
  {
    prop: "name",
    label: `${title}名称`,
    search: { el: "input" }
  },
  {
    prop: "orgName",
    label: "所属院区"
  },
  // {
  //   prop: "orgDepartmentName",
  //   label: "关联部门"
  // },
  {
    prop: "circle",
    label: "任务周期",
    render(scope) {
      const circleType = circleTypeOptions.find(item => item.value === scope.row.circleType)?.label;
      return `${scope.row.circle}${circleType}`;
    }
  },
  {
    prop: "devices",
    label: "设备名称",
    render(scope) {
      return scope.row.devices.map(item => item.name).join("、");
    }
  },
  {
    prop: "departments",
    label: "关联部门",
    render(scope) {
      return scope.row.departments.map(item => item.name).join("、");
    }
  },
  {
    prop: "executors",
    label: "关联人员",
    render(scope) {
      return scope.row.executors.map(item => item.name).join("、");
    }
  },
  {
    prop: "createdAt",
    label: "创建时间",
    render(scope) {
      return dayjs(scope.row.createdAt).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  {
    prop: "status",
    label: "计划状态",
    enum: statusOptions,
    tag: true
  },
  {
    prop: "remark",
    label: "备注"
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);
/**
 * 打开 drawer(新增、查看、编辑)
 */
const drawerRef = ref<InstanceType<typeof DetailDrawer> | null>(null);
async function openDrawer(title: "查看" | "新增" | "编辑", row: Partial<Plan.PlanRes> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: title === "新增" ? addPlan : title === "编辑" ? editPlan : undefined,
    getTableList: proTable.value?.getTableList
  };
  if (title !== "新增" && row.id) {
    const res = await getPlanDetail(row.id);
    params.row = res.data;
  }

  drawerRef.value?.acceptParams(params);
}

/** 作废计划 */
async function cancelPlanPatrol(id: number) {
  if (await useHandleData(deletePlan, id, "作废该计划")) {
    proTable.value?.getTableList();
  }
}
</script>
