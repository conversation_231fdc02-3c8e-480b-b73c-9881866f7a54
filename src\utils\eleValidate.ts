// ? Element 常用表单校验规则

export const phoneRegexp = /^1\d{10}$/;
/**
 *  @rule 手机号
 */
export function checkPhoneNumber(rule: any, value: any, callback: any) {
  // const regexp = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/;
  if (value === "") callback("请输入手机号码");
  if (!phoneRegexp.test(value)) {
    callback(new Error("请输入正确的手机号码"));
  } else {
    return callback();
  }
}

export const MaxDigitsAfterDecimal = /^\d+(\.\d{1,2})?$/;
export const MaxDigitsAfterDecimal3 = /^\d+(\.\d{1,3})?$/;
/**
 * @rule 金额
 */
export function checkMoney(rule: any, value: any, callback: any) {
  const regexp = MaxDigitsAfterDecimal;
  if (!regexp.test(value)) {
    callback(new Error("请输入正确的金额"));
  } else {
    return callback();
  }
}
