<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}设备`">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit" :disabled="drawerProps.isView">
      <el-form-item :error="errors.name" label="设备名称" required>
        <el-input v-bind="formData.name" />
      </el-form-item>
      <!-- <el-form-item :error="errors.code" label="设备编码" required>
        <el-input v-bind="formData.code" />
      </el-form-item> -->
      <el-form-item :error="errors.status" label="状态" required>
        <el-select v-bind="formData.status">
          <el-option v-for="item in statusOptions" :key="item.value" v-bind="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.orgId" label="医院" required>
        <el-select v-bind="formData.orgId">
          <el-option v-for="item in orgList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.orgDepartmentId" label="科室" required>
        <el-select v-bind="formData.orgDepartmentId">
          <el-option v-for="item in currentHospitalList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.systemType" label="归属系统" required>
        <el-select v-bind="formData.systemType">
          <el-option v-for="item in systemTypeOptions" :key="item.value" v-bind="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.address" label="地址">
        <el-input v-bind="formData.address" type="textarea" />
      </el-form-item>
      <el-form-item :error="errors.remark" label="备注">
        <el-input v-bind="formData.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="onSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="SparePartsDetailDrawer">
import { useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string, number } from "yup";
import { Device, systemTypeOptions, statusOptions } from "@/api/modules/device";
import { useOrgStore } from "@/stores/modules/orgId";
import { getDepartmentListByHospitalId } from "@/api/modules/organization";

interface DrawerProps {
  title?: "新增" | "编辑" | "查看";
  isView: boolean;
  row: Partial<Device.EquipmentRes>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    name: string().required().label("设备名称"),
    // code: string().required().label("设备编码"),
    orgDepartmentId: string().required().label("所属科室"),
    orgId: string().required().label("所属院区"),
    status: number().required().label("设备状态"),
    systemType: number().required().label("归属系统"),
    address: string().nullable().label("设备地址"),
    remark: string().nullable().label("备注")
  })
);

const { defineComponentBinds, handleSubmit, errors, setValues, resetForm } = useForm<Device.EquipmentRes>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  name: defineComponentBinds("name", elPlusConfig),
  // code: defineComponentBinds("code", elPlusConfig),
  orgId: defineComponentBinds("orgId", elPlusConfig),
  orgDepartmentId: defineComponentBinds("orgDepartmentId", elPlusConfig),
  status: defineComponentBinds("status", elPlusConfig),
  address: defineComponentBinds("address", elPlusConfig),
  systemType: defineComponentBinds("systemType", elPlusConfig),
  remark: defineComponentBinds("remark", elPlusConfig)
});

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: undefined,
  row: {}
});

/**
 * 医院列表
 */
const orgStore = useOrgStore();
const orgList = toRef(orgStore, "hospitalList");
const currentHospitalList = computedAsync(async () => {
  const currentHospital = formData.value.orgId.modelValue;
  if (!currentHospital) return [];
  const { data } = await getDepartmentListByHospitalId(currentHospital);
  if (data) return data;
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  if (params.title !== "新增") {
    setValues(params.row);
  } else {
    resetForm();
  }

  drawerVisible.value = true;
};

const onSubmit = handleSubmit(async values => {
  console.log(values);
  const orgDepartmentName = currentHospitalList.value?.find(item => item.id === values.orgDepartmentId)?.name;
  const orgName = orgList.value.find(item => item.id === values.orgId)?.name;

  await drawerProps.value.api!({
    ...values,
    orgDepartmentName,
    orgName
  });
  drawerVisible.value = false;
  drawerProps.value.getTableList!();
});

defineExpose({
  acceptParams
});
</script>
