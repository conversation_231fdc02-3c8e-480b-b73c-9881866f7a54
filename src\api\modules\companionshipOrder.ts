import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";
import { Gender, ServerType } from "@/utils/dict";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { type Bill } from "./billing";

export namespace CompanionshipOrder {
  /**
   * 订单状态
   */
  export enum OrderStatus {
    /**
     * 未知
     */
    Unknown = -1,
    /**
     * 取消(审核之前用户取消)
     */
    Cancelled = 1,
    /**
     * 已关闭(审核完毕后用户取消)
     */
    Closed = 2,
    /**
     * 已完结(手动触发,到期触发)
     */
    Completed = 3,
    /**
     * B端审核 院方未审核
     */
    BEndReview = 10,
    /**
     * 待物业确认
     */
    PendingPropertyConfirmation = 11,
    /**
     * 待完善信息(B端确认)
     */
    PendingInformationCompletion = 12,
    /**
     * 等待用户下单
     */
    User = 21,
    /**
     * 陪护中
     */
    Nursing = 30,
    /**
     * 结算申请(C端提交，B确认后才停止收费)
     */
    SettlementApplication = 31
  }

  /**
   * 折扣类型
   */
  export enum DiscountType {
    /**
     * 未知
     */
    Unknown = -1,
    /**
     * 影响已产生费用
     */
    AffectExistingCosts = 1,
    /**
     * 不影响已产生费用
     */
    DoNotAffectExistingCosts = 0
  }

  export interface BillParams {
    orderId: string;
    billType: Bill.BillType;
    billSubType?: Bill.BillSubType;
    price?: number;
  }

  /**
   * @name 陪护单
   */
  export interface Params {
    id?: string;
    /**
     * 下单人手机号
     */
    accountPhone: string;
    /**
     * 陪护订单状态
     */
    // orderStatus: OrderStatus;
    /**
     * 服务类型
     */
    serverType: ServerType;
    /**
     * 病人姓名
     */
    patientName: string;
    /**
     * 病人性别
     */
    patientGender: Gender;
    /**
     * 病人出生日期 yyyyMMdd
     */
    patientBirthday: string;
    /**
     * 家属联系电话
     */
    phone: string;
    /**
     * 机构ID(医院)
     */
    orgId: string;
    /**
     * 科室
     */
    departments: string;
    /**
     * 床号
     */
    bedNo?: string;
    /**
     * 陪护开始日期，格式：yyyyMMddHH
     */
    startTime: number;
    /**
     * 陪护终止日期，格式：yyyyMMddHH
     */
    endTime: number;
    /**
     * 陪护实际开始日期格式：yyyyMMddHH
     */
    realStartTime: number;
    /**
     * 陪护实际终止日期格式：yyyyMMddHH
     */
    realEndTime: number;
    /**
     * 病人病情描述
     */
    patientStateDesc: string;
    /**
     * 特殊要求
     */
    specificDesc: string;
    /**
     * 详细地址
     */
    address: string;
    /**
     * 备注
     */
    remark: string;
  }

  // 陪护记录接口
  export interface Record {
    /**
     * 陪护单
     */
    id: string;
    /**
     * 下单人手机号
     */
    accountPhone: string;
    /**
     * 订单C端用户ID
     */
    accountId: number;
    /**
     * 服务类型
     */
    serverType: ServerType;
    /**
     * 病人姓名
     */
    patientName: string;
    /**
     * 病人性别
     */
    patientGender: Gender;
    /**
     * 病人出生日期 yyyyMMdd
     */
    patientBirthday: string;
    /**
     * 家属联系电话
     */
    phone: string;
    /**
     * 病人病情描述
     */
    patientStateDesc: string;
    /**
     * 特殊要求
     */
    specificDesc: string;
    /**
     * 机构ID(医院)
     */
    orgId: string;
    /**
     * 科室
     */
    departments: string;
    /**
     * 床号
     */
    bedNo: string;
    /**
     * 详细地址
     */
    address: string;
    /**
     * 陪护订单状态
     */
    orderStatus: OrderStatus;
    /**
     * 陪护开始日期
     */
    startTime: string;
    /**
     * 陪护终止日期
     */
    endTime: string;
    /**
     * 陪护实际开始日期
     */
    realStartTime: string;
    /**
     * 陪护实际终止日期
     */
    realEndTime: string;
    /**
     * 总账单ID
     */
    parentBillId: number;
    /**
     * 折扣(99折)
     */
    discount: number;
    /**
     * 折扣类型
     */
    discountType: DiscountType;
    /**
     * 物业比例
     */
    rateCertifiedProperty: number;
    /**
     * 医院比例
     */
    rateHospital: number;
    /**
     * 护工比例
     */
    rateNursing: number;
    /**
     * 备注
     */
    remark: string;
  }

  export interface Bill {
    id: string;
    orderId: string;
    billType: number;
    billSubType: number;
    priceReceivable: number;
    priceReceived: number;
    remark: string;
  }

  /**
   * 陪护单详情接口
   */
  export interface Detail {
    /**
     * 陪护单
     */
    base: Record;
    /**
     * 账单
     */
    bill: Bill;
    curNursing: {
      id: string;
      name: string;
      time: number;
    };
    curNursingList: Array<Detail["curNursing"]>;
    preNursing?: Detail["curNursing"];
    preNursingList?: Array<Detail["curNursing"]>;
  }

  // 陪护记录响应接口
  export interface RecordsResponse {
    records: Record[];
  }
  /**
   * 陪护单修改记录
   */
  export interface OrderHistory {
    id: string;
    orderId: string;
    time: string;
    event: string;
    eventName: string;
    before: string;
    after: string;
    operatorId: string;
    operatorName: string;
    operatorMobile: string;
    operatorSource: number;
  }
}

/**
 * 陪护单状态
 */
export const orderStatusOptions = [
  { value: CompanionshipOrder.OrderStatus.Unknown, label: "未知" },
  { value: CompanionshipOrder.OrderStatus.Cancelled, label: "取消" },
  { value: CompanionshipOrder.OrderStatus.Closed, label: "已关闭" },
  { value: CompanionshipOrder.OrderStatus.Completed, label: "已完结" },
  { value: CompanionshipOrder.OrderStatus.BEndReview, label: "院方未审核" },
  { value: CompanionshipOrder.OrderStatus.PendingPropertyConfirmation, label: "物业未确认" },
  { value: CompanionshipOrder.OrderStatus.PendingInformationCompletion, label: "待完善" },
  { value: CompanionshipOrder.OrderStatus.User, label: "待下单  " },
  { value: CompanionshipOrder.OrderStatus.Nursing, label: "陪护中" },
  { value: CompanionshipOrder.OrderStatus.SettlementApplication, label: "申请提前完结" }
];

export const discountTypeOptions = [
  { value: CompanionshipOrder.DiscountType.DoNotAffectExistingCosts, label: "不影响已产生费用" },
  { value: CompanionshipOrder.DiscountType.AffectExistingCosts, label: "影响已产生费用" }
  // { value: CompanionshipOrder.DiscountType.Unknown, label: "未知" }
];

/**
 * @name 陪护单列表
 */
export const getCompanionshipOrderList = (params: ReqPage & { name?: string }) => {
  return http.post<ResPage<CompanionshipOrder.RecordsResponse>>(PORT1 + `/order/base/paging`, params, { loading: true });
};

/**
 * @name 创建陪护单
 */
export const createCompanionshipOrder = (params: Partial<CompanionshipOrder.Params>) => {
  return http.post(PORT1 + `/order/base/create`, params, { loading: true });
};

/**
 * 陪护单详情
 */
export const getCompanionshipOrderDetail = (orderId: string) => {
  return http.post<CompanionshipOrder.Detail>(PORT1 + `/order/base/detail`, { orderId }, { loading: true });
};

/**
 * @name 修改陪护单
 */
export const updateCompanionshipOrder = (params: Partial<CompanionshipOrder.Params>) => {
  return http.post(PORT1 + `/order/base/modify`, params, { loading: true });
};

/**
 * @name 院方审核
 */
export const auditCompanionshipOrder = (params: { orderId: string }) => {
  return http.post(PORT1 + `/order/base/commit/hospital`, params, { loading: true });
};

/**
 * @name 物业审核
 */
export const propertyAuditCompanionshipOrder = (params: { orderId: string }) => {
  return http.post(PORT1 + `/order/base/commit/certifiedProperty`, params, { loading: true });
};

/**
 * @name 修改账单
 */
export const updateBill = (params: CompanionshipOrder.BillParams) => {
  return http.post(PORT1 + `/order/bill/modify`, params, { loading: true });
};

/**
 * @name 修改绑定套餐
 */
export const updatePackage = (params: { orderId: string; itemIdList: string[] }) => {
  return http.post(PORT1 + `/order/base/modify/item`, params, { loading: true });
};

/**
 * @name 修改绑定护工
 * @param presetTime 生效时间(时间戳) -1 清除之前预设置 0 表示立即 时间戳戳:指定时间戳戳
 */
export const updateNursing = (params: { orderId: string; nursingIdList: Array<string>; presetTime?: number }) => {
  return http.post(PORT1 + `/order/base/modify/nursing`, params, { loading: true });
};

/**
 * @name 确认陪护单
 */
export const confirmCompanionshipOrder = (params: { orderId: string }) => {
  return http.post(PORT1 + `/order/base/commit/completeInfo`, params, { loading: true });
};

/**
 * @name 设置折扣
 */
export const setDiscount = (params: { orderId: string; discount: number; type: CompanionshipOrder.DiscountType }) => {
  return http.post(PORT1 + `/order/base/modify/discount`, params, { loading: true });
};

/**
 * @name 修改分成
 */
export const updateRate = (params: {
  orderId: string;
  rateCertifiedProperty: number;
  rateHospital: number;
  rateNursing: number;
  type: number;
}) => {
  return http.post(PORT1 + `/order/base/modify/rate`, params, { loading: true });
};

/**
 * @name 陪护单修改记录
 */
export const ordreHistory = (params: ReqPage & { orderId?: string }) => {
  return http.post<ResPage<CompanionshipOrder.OrderHistory>>(PORT1 + `/order/changelog/paging`, params, { loading: true });
};

/**
 * @name 陪护单提前结束
 */
export const orderEnd = (params: { orderId: string; settleApplyOperate: number }) => {
  return http.post(PORT1 + `/order/base/commit/settle`, params, { loading: true });
};
