<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="() => router.push({ name: 'accountAdd' })">新增账号</el-button>
        <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除
        </el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Lock" @click="resetPassword(scope.row)">重置密码</el-button>
        <el-link
          type="primary"
          :icon="View"
          :underline="false"
          :href="toDetail({ name: 'accountDetail', params: { id: scope.row.id } })"
        >
          查看
        </el-link>
        <el-link
          type="primary"
          :icon="EditPen"
          :underline="false"
          :href="toDetail({ name: 'accountEdit', params: { id: scope.row.id } })"
        >
          编辑
        </el-link>
        <el-button type="primary" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </ProTable>
    <ResetPasswordDialog v-model:dialog-visible="resetPasswordShow" :account-id="currentAccountId" />
  </div>
</template>

<script setup lang="tsx" name="accountManage">
import { User, Hospital } from "@/api/interface/index";
import { getUserList, editUser, deleteUser, getUserDetail } from "@/api/modules/user";
import { getResourceData } from "@/api/modules/hospital";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { CirclePlus, Delete, EditPen, View, Lock } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { useHandleData } from "@/hooks/useHandleData";
import { RouteLocationRaw, useLink, useRouter } from "vue-router";
import { onMounted } from "vue";
import ResetPasswordDialog from "./components/ResetPasswordDialog.vue";
import { StatusEnum } from "@/utils/dict";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();
const router = useRouter();

/**
 * 医院数据
 */
const hospitalList = ref<Hospital.ResResourceData[]>([]);

/**
 * 获取医院数据
 */
async function getHospitalList() {
  try {
    const { data } = await getResourceData();
    hospitalList.value = data;
  } catch (error) {
    console.error("获取医院数据失败:", error);
  }
}

/**
 * 根据医院ID获取医院名称
 */
function getHospitalName(hospitalId: string | number | undefined): string {
  if (!hospitalId) return "-";
  const hospital = hospitalList.value.find(item => item.id === String(hospitalId));
  return hospital ? hospital.name : `未知(${hospitalId})`;
}

/**
 * 查询列表参数
 */
const getTableList = (params: any) => {
  return getUserList(params);
};

const renderTime = (time: string) => {
  return (
    <div>
      <span>{dayjs(time).format("YYYY-MM-DD HH:mm:ss")}</span>
    </div>
  );
};

const columns = reactive<ColumnProps<User.ResUserList>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "nickname",
    label: "用户名"
  },
  {
    prop: "phoneNumber",
    label: "手机号",
    width: 160,
    search: { el: "input" }
  },
  {
    prop: "hospitalId",
    label: "所属医院",
    width: 160,
    render: scope => {
      return (
        <span>{getHospitalName(scope.row.hospitalId)}</span>
      );
    }
  },
  {
    prop: "status",
    label: "状态",
    render: scope => {
      return (
        <el-switch model-value={scope.row.status} active-value={1} inactive-value={0} onChange={() => changeStatus(scope.row)} />
      );
    }
  },
  {
    prop: "createdAt",
    label: "创建时间",
    render: scope => renderTime(scope.row.createdAt)
  },
  {
    prop: "createdAt",
    label: "更新时间",
    render: scope => renderTime(scope.row.createdAt)
  },
  {
    width: 280,
    prop: "operation",
    label: "操作",
    fixed: "right"
  }
]);

function toDetail(route: RouteLocationRaw) {
  return unref(useLink({ to: route }).href);
}

/**
 * 删除信息
 */
const handleDelete = async (params: User.ResUserList) => {
  await useHandleData(deleteUser, [params.id], `删除【${params.nickname}】用户`);
  proTable.value?.getTableList();
};

/**
 * 批量删除信息
 */
async function batchDelete(selectedListIds: string[]) {
  await useHandleData(deleteUser, selectedListIds, "删除所选用户信息");
  proTable.value?.getTableList();
}

/**
 * 禁用启用
 * @param row
 */
async function changeStatus(row: User.ReqUserParams) {
  const { data } = await getUserDetail(row.id!);
  const params = {
    id: data.id,
    avatarUrl: data.avatarUrl,
    nickname: data.nickname,
    departmentIds: data.departmentList?.map(item => item.id),
    organizationIds: data.organizationList?.map(item => item.id),
    phoneNumber: data.phoneNumber,
    type: data.type,
    status: row.status === StatusEnum.NO ? StatusEnum.OFF : StatusEnum.NO
  };

  if (row.status === StatusEnum.NO) {
    await useHandleData(editUser, params, `禁用【${row.nickname}】用户`);
  } else {
    await useHandleData(editUser, params, `启用【${row.nickname}】用户`);
  }

  proTable.value?.getTableList();
}

const resetPasswordShow = ref(false);
const currentAccountId = ref("");
/**
 * 重置密码
 */
async function resetPassword(row: User.ReqUserParams) {
  resetPasswordShow.value = true;
  currentAccountId.value = row.id!;
}

// 组件挂载时获取医院数据
onMounted(() => {
  getHospitalList();
});
</script>
