import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";
import { StatusEnum } from "@/utils/dict";

export namespace Role {
  export interface ResRole {
    id: string;
    name: string;
    roleKey: string;
    status: StatusEnum;
    menuIds?: number[];
    remark: string;
    createBy: string;
    updateBy: string;
    sort: number;
    createdAt: string;
    updateAt: string;
  }

  export interface RoleMenu {
    id: number;
    name: string;
    parentId: number;
    menuKey: string;
    level: number;
    status: StatusEnum;
    sort: number;
    remark: string;
  }
}

/**
 * @name 角色列表
 */
export const getRoleList = (params: ReqPage & { name?: string }, loading: Boolean = true) => {
  return http.get<ResPage<Role.ResRole>>(PORT1 + `/role/paging`, params, { loading });
};

/**
 * @name 角色详情
 */
export const getRoleDetail = (id: string) => {
  return http.get<Role.ResRole>(PORT1 + `/role/${id}`, {}, { loading: true });
};

/**
 * @name 新增角色
 */

export const addRole = (params: Role.ResRole) => {
  return http.post<Role.ResRole>(PORT1 + `/role`, params, { loading: true });
};

/**
 * @name 编辑角色
 */
export const editRole = (params: Role.ResRole) => {
  return http.put<Role.ResRole>(PORT1 + `/role`, params, { loading: true });
};

/**
 * @name 删除角色
 */
export const deleteRole = (ids: string[]) => {
  return http.put<Role.ResRole>(PORT1 + `/role/delete`, { ids }, { loading: true });
};

/**
 * 查询角色对应的菜单权限
 */
export const getRoleMenu = (id: string) => {
  return http.get<{
    menuList: Role.RoleMenu[];
  }>(PORT1 + `/role/menu`, { roleId: id }, { loading: true });
};

export const setRoleMenu = (params: { roleId: string; menuIds: number[] }) => {
  return http.post(PORT1 + `/role/allocation-menu`, params, { loading: true });
};
