import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";

export namespace Cashlog {
  /** 操作类型 */
  export enum OperationType {
    /**
     * 增加
     */
    Add = 1,
    /**
     * 提取
     */
    Withdraw = 2
  }
  export enum BusinessType {
    /**
     * 人工提取
     */
    ManualWithdrawal = 0,
    /**
     * 账单支付
     */
    BillCashWithdrawal = 1,
    /**
     * 人工存入
     */
    ManualDeposit = 2,
    /**
     * 支付宝存入
     */
    AlipayDeposit = 3,
    /**
     * 支付宝提取
     */
    AlipayWithdrawal = 4
  }
  export interface Record {
    id: string;
    type: OperationType;
    price: number;
    bizType: BusinessType;
    bizId: string;
    personId: string;
    personName: string;
    createAt: string;
  }
}

export const operationTypeOptions = [
  { value: Cashlog.OperationType.Add, label: "增加" },
  { value: Cashlog.OperationType.Withdraw, label: "提取" }
];

export const businessTypeOptions = [
  { value: Cashlog.BusinessType.ManualWithdrawal, label: "人工提取" },
  { value: Cashlog.BusinessType.BillCashWithdrawal, label: "账单支付" },
  { value: Cashlog.BusinessType.ManualDeposit, label: "人工存入" },
  { value: Cashlog.BusinessType.AlipayDeposit, label: "支付宝支付" },
  { value: Cashlog.BusinessType.AlipayWithdrawal, label: "支付宝提取" }
];

/**
 * @name 现金归档列表
 */
export const getCashlogList = (params: ReqPage & { type?: string }) => {
  return http.post<ResPage<Cashlog.Record>>(PORT1 + `/cashlog/paging`, params, { loading: true });
};

/**
 * 现金提取
 */
export const extract = (params: { personId?: string; personName?: string; price?: number }) => {
  return http.post(PORT1 + `/cashlog/extract`, params, { loading: true });
};

/**
 * 现金存入
 */
export const deposit = (params: { personId?: string; personName?: string; price?: number }) => {
  return http.post(PORT1 + `/cashlog/deposit`, params, { loading: true });
};

/**
 * 查询余额
 */
export const getBalance = () => {
  return http.post<{ price: number; alipayPrice: number }>(PORT1 + `/cashlog/balance`, {}, { loading: true });
};
