<template>
  <el-dialog title="请设置分成" v-model="visible">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit">
      <el-form-item label="物业比例" :error="errors.rateCertifiedProperty" required>
        <el-input v-bind="formData.rateCertifiedProperty"><template #append>%</template></el-input>
      </el-form-item>
      <el-form-item label="医院比例" :error="errors.rateHospital" required>
        <el-input v-bind="formData.rateHospital"><template #append>%</template></el-input>
      </el-form-item>
      <el-form-item label="护工比例" :error="errors.rateNursing" required>
        <el-input v-bind="formData.rateNursing"><template #append>%</template></el-input>
      </el-form-item>
      <el-form-item label="是否影响已产生费用" :error="errors.type" required>
        <!-- @vue-ignore -->
        <el-radio-group v-bind="formData.type">
          <el-radio v-for="item in discountTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="onSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useCompanionshipOrderStore, DialogType } from "@/stores/modules/companionshipOrder";
import { CompanionshipOrder, discountTypeOptions, updateRate } from "@/api/modules/companionshipOrder";
import BigNumber from "bignumber.js";
import { toTypedSchema } from "@vee-validate/yup";
import { object, number } from "yup";
import { useForm } from "vee-validate";
import { MaxDigitsAfterDecimal, MaxDigitsAfterDecimal3 } from "@/utils/eleValidate";
import { convertProportionToThousandth, priceToCent, roundPriceByThousand } from "@/utils";
const companionshipOrderStore = useCompanionshipOrderStore();

let orderId: string;

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    rateCertifiedProperty: number()
      .min(0)
      .max(100)
      .transform(value => (isNaN(value) ? undefined : value))
      .required("请输入正确的分成")
      .test("maxDigitsAfterDecimal", "分成必须是一个正数，且最多保留三位小数", number =>
        MaxDigitsAfterDecimal3.test(number.toString())
      )
      .label("物业比例"),
    rateHospital: number()
      .min(0)
      .max(100)
      .transform(value => (isNaN(value) ? undefined : value))
      .required("请输入正确的分成")
      .test("maxDigitsAfterDecimal3", "分成必须是一个正数，且最多保留三位小数", number =>
        MaxDigitsAfterDecimal3.test(number.toString())
      )
      .label("医院比例"),
    rateNursing: number()
      .min(0)
      .max(100)
      .transform(value => (isNaN(value) ? undefined : value))
      .required("请输入正确的分成")
      .test("maxDigitsAfterDecimal3", "分成必须是一个正数，且最多保留三位小数", number =>
        MaxDigitsAfterDecimal3.test(number.toString())
      )
      .test("sum-100", "比例的和必须等于100", function (value, { parent }) {
        const { rateCertifiedProperty, rateHospital, rateNursing } = parent;
        return BigNumber.sum(rateCertifiedProperty, rateHospital, rateNursing).toNumber() === 100;
      })
      .label("护工比例"),
    type: number().required("请选择是否影响已产生费用").label("是否影响已产生费用")
  })
);

const { defineComponentBinds, handleSubmit, resetForm, errors, setValues } = useForm<{
  rateCertifiedProperty: string;
  rateHospital: string;
  rateNursing: string;
  type: number;
}>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  rateCertifiedProperty: defineComponentBinds("rateCertifiedProperty", elPlusConfig),
  rateHospital: defineComponentBinds("rateHospital", elPlusConfig),
  rateNursing: defineComponentBinds("rateNursing", elPlusConfig),
  type: defineComponentBinds("type", elPlusConfig)
});

const DIALOGTYPE = DialogType.ShareRatio;
const visible = computed<boolean>({
  set(val) {
    if (val) {
      companionshipOrderStore.dialogInfo.dialogVisible = DIALOGTYPE;
    } else {
      companionshipOrderStore.dialogInfo.dialogVisible = DialogType.None;
    }
    resetForm();
  },
  get() {
    if (companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE) {
      orderId = companionshipOrderStore.dialogInfo.info!.id;
      const rateCertifiedProperty = companionshipOrderStore.dialogInfo.info?.rateCertifiedProperty;
      const rateHospital = companionshipOrderStore.dialogInfo.info?.rateHospital;
      const rateNursing = companionshipOrderStore.dialogInfo.info?.rateNursing;
      if (rateCertifiedProperty && rateCertifiedProperty !== -1) {
        setValues({
          rateCertifiedProperty: convertProportionToThousandth(rateCertifiedProperty)?.toString(),
          rateHospital: convertProportionToThousandth(rateHospital)?.toString(),
          rateNursing: convertProportionToThousandth(rateNursing)?.toString(),
          type: CompanionshipOrder.DiscountType.DoNotAffectExistingCosts
        });
      }
    }
    return companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE;
  }
});

const onSubmit = handleSubmit(async values => {
  console.log(values);

  const params = {
    orderId: orderId,
    rateCertifiedProperty: roundPriceByThousand(values.rateCertifiedProperty)!,
    rateHospital: roundPriceByThousand(values.rateHospital)!,
    rateNursing: roundPriceByThousand(values.rateNursing)!,
    type: values.type
  };

  await updateRate(params);
  ElMessage("修改成功");
  companionshipOrderStore.dialogInfo = {
    info: undefined,
    dialogVisible: DialogType.None
  };
  companionshipOrderStore.getTableList!();
});
</script>
