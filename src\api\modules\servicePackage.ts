import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";
import { ServerType, StatusEnum } from "@/utils/dict";

export namespace ServerPackage {
  export interface ResServerPackage {
    /**
     * 套餐ID
     */
    id: string;
    /**
     * 套餐类型
     */
    serverType: ServerType;
    /**
     * 套餐名称
     */
    name: string;
    /**
     * 服务价格(单位：分/天)
     */
    price: number;
    /**
     * 状态(影响C端展示)：1 上架，0 下架
     */
    status: StatusEnum;
    /**
     * 护工星级 1 ~ 5 星
     */
    nursingStar: number;
    /**
     * 详细描述
     */
    remark: string;
    /**
     * 封面图
     */
    coverPicture: string;
    /**
     * 物业管理费比例
     */
    rateCertifiedProperty: number;
    /**
     * 院方管理费比例
     */
    rateHospital: number;
    /**
     * 护工费比例
     */
    rateNursing: number;
    /**
     * 机构ID
     */
    orgIdList: string[];
    /**
     * 计价终止时间
     */
    chargingTime: number;
    /**
     * 表示c端外显示 1 是 0 不是
     */
    display: number;
  }

  export interface ServerPackageParams {
    id?: string;
    serverType: ServerType; // 套餐类型
    name: string; // 套餐名称
    price: number; // 服务价格(单位：分/天)
    nursingStar?: 1 | 2 | 3 | 4 | 5; // 护工星级 1 ~ 5 星
    remark?: string; // 详细描述
    coverPicture?: string; // 封面图
    orgIdList: number[]; // 机构ID
    chargingTime: number; // 时间节点 0 ~ 23
    operator?: number; // 操作人
    /**
     * 物业管理费比例
     */
    rateCertifiedProperty: number;
    /**
     * 院方管理费比例
     */
    rateHospital: number;
    /**
     * 护工费比例
     */
    rateNursing: number;
  }
}

/**
 * @name 套餐列表
 */
export const getServerPackageList = (params: ReqPage & { name?: string }, loading: Boolean = true) => {
  return http.post<ResPage<ServerPackage.ResServerPackage>>(PORT1 + `/item/paging`, params, { loading });
};

/**
 * @name 套餐详情
 */
export const getServerPackageDetail = (id: string) => {
  return http.post<ServerPackage.ServerPackageParams>(PORT1 + `/item/get`, { itemId: id }, { loading: true });
};

/**
 * @name 新增套餐
 */

export const addServerPackage = (params: ServerPackage.ResServerPackage) => {
  return http.post<ServerPackage.ResServerPackage>(PORT1 + `/item/create`, params, { loading: true });
};

/**
 * @name 编辑套餐
 */
export const editServerPackage = (params: ServerPackage.ResServerPackage) => {
  return http.post<ServerPackage.ResServerPackage>(PORT1 + `/item/modify`, params, { loading: true });
};

// /**
//  * @name 删除套餐
//  */
// export const deleteServerPackage = (ids: string[]) => {
//   return http.post<ServerPackage.ResServerPackage>(PORT1 + `/item/delete`, { ids }, { loading: true });
// };

/**
 * @name 套餐类型枚举
 */
export const getServerPackageType = () => {
  return http.post<{
    [key in ServerType]?: string;
  }>(PORT1 + `/item/enum/serveType`, {}, { loading: true });
};
/**
 * 上架
 */
export const upServerPackage = (id: string) => {
  return http.post<ServerPackage.ResServerPackage>(PORT1 + `/item/no`, { itemId: id }, { loading: true });
};

/**
 * 下架
 */
export const downServerPackage = (id: string) => {
  return http.post(PORT1 + `/item/off`, { itemId: id }, { loading: true });
};

/**
 * 设置显示
 */
export const showPackage = (id: string) => {
  return http.post<ServerPackage.ResServerPackage>(PORT1 + `/item/no-display`, { itemId: id }, { loading: true });
};

/**
 * 设置隐藏
 */
export const hidePackage = (id: string) => {
  return http.post<ServerPackage.ResServerPackage>(PORT1 + `/item/off-display`, { itemId: id }, { loading: true });
};
