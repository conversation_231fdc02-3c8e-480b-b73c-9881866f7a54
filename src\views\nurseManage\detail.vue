<template>
  <div class="card content-box">
    <el-form ref="ruleFormRef" label-width="120px" label-suffix=" :" :disabled="isView" :rules="rules" :model="nurseDetail">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="nurseDetail.name" placeholder="请输入姓名" show-word-limit maxlength="10"></el-input>
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="nurseDetail.gender" clearable>
          <el-option v-for="item in genderType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input v-model="nurseDetail.age" placeholder="请输入年龄"></el-input>
      </el-form-item>
      <el-form-item label="服务年限" prop="workYear">
        <el-input v-model="nurseDetail.workYear" placeholder="请输入服务年限"></el-input>
      </el-form-item>
      <el-form-item label="联系方式" prop="mobile">
        <el-input v-model="nurseDetail.mobile" placeholder="请输入联系方式"></el-input>
      </el-form-item>
      <el-form-item label="星级" prop="nursingStar">
        <el-rate v-model="nurseDetail.nursingStar" clearable></el-rate>
      </el-form-item>
      <el-form-item label="所属院区" prop="orgIds">
        <el-select class="w-full" v-model="nurseDetail.orgIds" multiple>
          <el-option v-for="item in orgList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="介绍人" prop="sponsor" maxlength="10">
        <el-input v-model="nurseDetail.sponsor" show-word-limit placeholder="请输入介绍人"></el-input>
      </el-form-item>
      <el-form-item label="介绍费" prop="sponsorPrice" maxlength="10">
        <el-input v-model="nurseDetail.sponsorPrice" placeholder="请输入介绍费"></el-input>
      </el-form-item>
      <el-form-item label="费用结算方式" prop="settleWay" clearable>
        <el-select v-model="nurseDetail.settleWay">
          <el-option v-for="item in settleWayType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="费用结算时间" prop="settleTime">
        <div class="flex text-nowrap w-50">
          工作满
          <el-input class="mx-1" v-model="nurseDetail.settleTime" placeholder="请输入"></el-input> 月后结算
        </div>
      </el-form-item>
      <el-form-item label="特长" prop="specialty" maxlength="10240">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 8 }"
          v-model="nurseDetail.specialty"
          show-word-limit
          placeholder="请输入特长"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark" maxlength="10240">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 8 }"
          v-model="nurseDetail.remark"
          show-word-limit
          placeholder="请输入备注"
        ></el-input>
      </el-form-item>
      <el-form-item label="证件照" prop="idCardPic">
        <UploadImg v-model:image-url="nurseDetail.idCardPic" width="135px" height="135px" :file-size="3">
          <template #empty>
            <el-icon><Avatar /></el-icon>
            <span>请上传证件照</span>
          </template>
          <template #tip> 照片大小不能超过 3M </template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="资质与证明" prop="certificationFileList">
        <UploadImgs v-model:file-list="nurseDetail.certificationFileList" width="135px" height="135px" :file-size="3">
          <template #empty>
            <el-icon><Avatar /></el-icon>
            <span>请上传图片</span>
          </template>
          <template #tip> 图片大小不能超过 3M </template>
        </UploadImgs>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="UserDetail">
import UploadImg from "@/components/Upload/Img.vue";
import UploadImgs from "@/components/Upload/Imgs.vue";
import { Nurse, addNurse, editNurse, getNurseDetail, settleWayType } from "@/api/modules/nurseManage";
import { useTabsStore } from "@/stores/modules/tabs";
import type { ElForm, FormRules, UploadUserFile } from "element-plus";
import { genderType } from "@/utils/dict";
import { checkMoney } from "@/utils/eleValidate";
import { useOrgStore } from "@/stores/modules/orgId";
import { priceFormat, priceToCent } from "@/utils";

const rules = reactive<FormRules>({
  name: [{ required: true, message: "请填写护工名称" }],
  orgIds: [{ required: true, message: "请选择所属院区" }],
  age: [
    {
      type: "number",
      asyncValidator: (rule, value) => {
        return new Promise((resolve, reject) => {
          if (value == undefined) return resolve();
          else if (!Number.isInteger(Number(value))) reject("请输入正确的年龄");
          else if (value < 16) {
            reject("年龄不能小于16岁");
          } else if (value > 80) {
            reject("年龄不能大于80岁");
          } else {
            resolve();
          }
        });
      }
    }
  ],
  workYear: [
    {
      type: "number",
      asyncValidator: (rule, value) => {
        return new Promise((resolve, reject) => {
          if (value === undefined) return resolve();
          else if (!Number.isInteger(Number(value))) reject("请输入正确的工作年限");
          else if (value < 0) {
            reject("工作年限不能小于0年");
          } else if (value > 80) {
            reject("工作年限不能大于80年");
          } else {
            resolve();
          }
        });
      }
    }
  ],
  sponsorPrice: [
    {
      type: "number",
      asyncValidator: (rule, value, callback) => {
        if (value === undefined) return callback();
        checkMoney(rule, value, callback);
      }
    }
  ],
  settleTime: [
    {
      type: "number",
      asyncValidator: (rule, value) => {
        return new Promise((resolve, reject) => {
          if (value == undefined) return resolve();
          else if (!Number.isInteger(Number(value))) reject("请输入正确的费用结算时间");
          else {
            resolve();
          }
        });
      }
    }
  ]
});

const nurseDetail = ref<
  Nurse.ParamsNurse & {
    id?: string;
    certificationFileList?: UploadUserFile[];
  }
>({
  orgIds: [],
  name: "",
  certificationFileList: []
});

const tabStore = useTabsStore();
const route = useRoute();
const routeName = route.name as "nurseAdd" | "nurseDetail" | "nurseEdit";
const isView = ref(false);

const orgStore = useOrgStore();
const orgList = toRef(orgStore, "hospitalList");

const ruleFormRef = ref<InstanceType<typeof ElForm>>();

/**
 * 提交表单
 */
function onSubmit() {
  ruleFormRef.value?.validate(async validate => {
    if (!validate) return;
    const params = {
      ...nurseDetail.value,
      certificationPicList: nurseDetail.value.certificationFileList?.length
        ? nurseDetail.value.certificationFileList?.map(item => item.url!)
        : undefined,
      sponsorPrice: priceToCent(nurseDetail.value.sponsorPrice)
    };
    params.certificationFileList && delete params.certificationFileList;

    if (routeName === "nurseAdd") {
      await addNurse(params);
    } else if (routeName === "nurseEdit") {
      await editNurse(params);
    }
    tabStore.removeTabs(route.fullPath);
  });
}

async function init() {
  if (route.params.id && typeof route.params.id === "string") {
    const { data } = await getNurseDetail(route.params.id);

    const detail: Nurse.ParamsNurse & { certificationFileList: UploadUserFile[]; hospitalList?: any[] } = {
      ...data,
      orgIds: data.hospitalList.map(item => item.id),
      certificationFileList: data.certificationPicList?.map(item => ({ name: item.split(".").pop() || "", url: item })) || [],
      sponsorPrice: priceFormat(data.sponsorPrice)
    };

    delete detail.hospitalList;

    nurseDetail.value = detail;
    if (routeName === "nurseDetail") {
      tabStore.setTabsTitle(`${data.name}-详情`);
      isView.value = true;
    } else if (routeName === "nurseEdit") {
      tabStore.setTabsTitle(`${data.name}-编辑`);
    }
  }
}

init();
</script>

<style scoped>
.el-form {
  width: 100%;
  margin-top: 20px;
}
</style>
