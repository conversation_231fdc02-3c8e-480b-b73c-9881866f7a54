import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";

export namespace Evaluation {
  export enum Source {
    Unknown = -1,
    User = 1,
    Hospital = 2
  }

  export interface Record {
    id: string;
    orderId: string;
    level: number;
    remark: string;
    source: Source;
  }
}

export const sourceOptions = [
  // { value: Evaluation.Source.Unknown, label: "未知" },
  { value: Evaluation.Source.User, label: "用户" },
  { value: Evaluation.Source.Hospital, label: "医院" }
];

/**
 * @name 订单评价
 */
export const getOrderEvaluation = (params: ReqPage) => {
  return http.post<ResPage<Evaluation.Record>>(PORT1 + `/order/estimate/paging`, params, { loading: true });
};
