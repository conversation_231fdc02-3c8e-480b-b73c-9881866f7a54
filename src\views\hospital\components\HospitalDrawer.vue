<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}院区`">
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <el-form-item label="院区名称" prop="name">
        <el-input v-model="drawerProps.row!.name" placeholder="请输入院区名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="院区地址" prop="address">
        <el-input v-model="drawerProps.row!.address" placeholder="请输入院区地址" clearable></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="drawerProps.row!.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入备注信息"
          maxlength="255"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="启用状态" prop="enable">
        <el-switch
          v-model="drawerProps.row!.enable"
          :active-value="1"
          :inactive-value="0"
          active-text="启用"
          inactive-text="禁用"
        ></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="HospitalDrawer">
import { reactive, ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { Hospital } from "@/api/modules/hospital";
import type { FormInstance, FormRules } from "element-plus/es/components/form";

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入院区名称", trigger: "blur" },
    { min: 1, max: 128, message: "院区名称长度应在1-128个字符之间", trigger: "blur" }
  ],
  address: [
    { required: true, message: "请输入院区地址", trigger: "blur" },
    { min: 1, max: 255, message: "院区地址长度应在1-255个字符之间", trigger: "blur" }
  ],
  remark: [
    { max: 255, message: "备注长度不能超过255个字符", trigger: "blur" }
  ],
  enable: [
    { required: true, message: "请选择启用状态", trigger: "change" }
  ]
});

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<Hospital.ReqHospitalParams>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  // 设置默认值
  if (drawerProps.value.title === "新增" && !drawerProps.value.row.enable) {
    drawerProps.value.row.enable = 1;
  }
  drawerVisible.value = true;
};

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await drawerProps.value.api!(drawerProps.value.row);
      ElMessage.success({ message: `${drawerProps.value.title}院区成功！` });
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.el-form {
  padding: 20px;
}
</style> 