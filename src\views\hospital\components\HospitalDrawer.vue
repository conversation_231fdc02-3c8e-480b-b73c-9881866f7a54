<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}院区`">
    <el-form
      ref="ruleFormRef"
      label-width="100px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <el-form-item label="院区名称" prop="name">
        <el-input v-model="drawerProps.row!.name" placeholder="请输入院区名称" clearable maxlength="128" show-word-limit />
      </el-form-item>

      <el-form-item label="院区地址" prop="address">
        <el-input v-model="drawerProps.row!.address" placeholder="请输入院区地址" clearable maxlength="255" show-word-limit />
      </el-form-item>

      <el-form-item label="启用状态" prop="enable">
        <el-switch
          v-model="drawerProps.row!.enable"
          :active-value="1"
          :inactive-value="0"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="drawerProps.row!.remark"
          type="textarea"
          placeholder="请输入备注信息"
          clearable
          maxlength="255"
          show-word-limit
          :rows="4"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="HospitalDrawer">
import { ref, reactive } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { Hospital } from "@/api/interface/index";
import { addHospital, editHospital } from "@/api/modules/hospital";

/**
 * 表单验证规则
 */
const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入院区名称", trigger: "blur" },
    { min: 1, max: 128, message: "院区名称长度在 1 到 128 个字符", trigger: "blur" }
  ],
  address: [
    { required: true, message: "请输入院区地址", trigger: "blur" },
    { min: 1, max: 255, message: "院区地址长度在 1 到 255 个字符", trigger: "blur" }
  ],
  enable: [{ required: true, message: "请选择启用状态", trigger: "change" }],
  remark: [{ max: 255, message: "备注长度不能超过 255 个字符", trigger: "blur" }]
});

/**
 * 抽屉属性接口
 */
interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<Hospital.ResHospitalList>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

/**
 * 抽屉状态
 */
const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

/**
 * 接收父组件传过来的参数
 */
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;

  // 设置默认值
  if (drawerProps.value.title === "新增") {
    drawerProps.value.row = {
      name: "",
      address: "",
      remark: "",
      enable: 1
    };
  }

  drawerVisible.value = true;
};

/**
 * 提交数据（新增/编辑）
 */
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;

    try {
      const params: Hospital.ReqHospitalParams = {
        name: drawerProps.value.row!.name!,
        address: drawerProps.value.row!.address!,
        remark: drawerProps.value.row!.remark || "",
        enable: drawerProps.value.row!.enable
      };

      // 如果是编辑，需要传递 id
      if (drawerProps.value.title === "编辑" && drawerProps.value.row!.id) {
        params.id = drawerProps.value.row!.id;
      }

      // 调用对应的 API
      const api = drawerProps.value.title === "新增" ? addHospital : editHospital;
      await api(params);

      ElMessage.success({ message: `${drawerProps.value.title}院区成功！` });
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

/**
 * 暴露给父组件的方法
 */
defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-switch {
    --el-switch-on-color: var(--el-color-primary);
    --el-switch-off-color: var(--el-color-info);
  }
}
</style>
