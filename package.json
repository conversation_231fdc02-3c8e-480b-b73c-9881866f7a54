{"name": "geeker-admin", "private": true, "version": "1.2.0", "type": "module", "description": "geeker-admin open source management system", "author": {"name": "<PERSON>ker", "email": "<EMAIL>", "url": "https://github.com/HalseySpicy"}, "license": "MIT", "homepage": "https://github.com/HalseySpicy/Geeker-Admin", "repository": {"type": "git", "url": "**************:HalseySpicy/Geeker-Admin.git"}, "bugs": {"url": "https://github.com/HalseySpicy/Geeker-Admin/issues"}, "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vue-tsc && vite build --mode development", "build:test": "vue-tsc && vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "release": "standard-version", "commit": "czg && git push"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vee-validate/yup": "^4.11.8", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.5.0", "bignumber.js": "^9.1.2", "dayjs": "^1.11.9", "driver.js": "^1.3.0", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.4.1", "lodash-es": "^4.17.21", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "vue": "^3.3.4", "vue-i18n": "^9.4.0", "vue-router": "^4.2.4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@types/lodash-es": "^4.17.10", "@types/md5": "^2.3.2", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.8", "@types/sortablejs": "^1.15.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@unocss/eslint-config": "^0.57.1", "@vitejs/plugin-vue": "^4.3.4", "@vitejs/plugin-vue-jsx": "^3.0.2", "autoprefixer": "^10.4.15", "cz-git": "^1.7.1", "czg": "^1.7.1", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "postcss": "^8.4.29", "postcss-html": "^1.5.0", "prettier": "^3.0.3", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.66.1", "standard-version": "^9.5.0", "stylelint": "^15.10.3", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended-scss": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.0.0", "typescript": "^5.1.6", "unocss": "^0.57.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "unplugin-vue-setup-extend-plus": "^1.0.0", "vee-validate": "^4.11.8", "vite": "^4.4.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-pwa": "^0.16.5", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.11", "yup": "^1.3.2"}, "engines": {"node": ">=16.0.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}