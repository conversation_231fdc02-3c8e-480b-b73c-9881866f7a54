<template>
  <el-dialog v-model="visible" title="修改密码" width="30%">
    <div>
      <el-input v-model="newpassword" type="password" show-password placeholder="请输入新密码"></el-input>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleResetPassword"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="ResetPasswordDialog">
import { resetUserPassWord } from "@/api/modules/user";

const props = defineProps({
  accountId: {
    type: String,
    required: true
  },
  dialogVisible: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits<{
  "update:dialogVisible": [value: boolean];
}>();

const visible = computed({
  get() {
    return props.dialogVisible;
  },
  set(value: boolean) {
    emit("update:dialogVisible", value);
  }
});

const newpassword = ref("");
async function handleResetPassword() {
  await resetUserPassWord({
    accountId: props.accountId,
    newPassword: newpassword.value
  });
  visible.value = false;
  ElMessage.success("重置密码成功");
}
</script>
