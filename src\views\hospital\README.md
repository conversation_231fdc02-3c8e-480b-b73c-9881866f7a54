# 院区管理功能

## 功能概述

院区管理模块提供了完整的院区信息管理功能，包括院区的增删改查、状态管理、批量导入导出等功能。

## 数据库表结构

```sql
CREATE TABLE `hospital` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '院区名称',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '院区地址',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `enable` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '启用状态: 0=>禁用; 1=>启用',
  `deleted` bigint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 1 未删除;已删除:时间戳',
  `create_by` bigint unsigned NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint unsigned NOT NULL COMMENT '更新人',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2109098013452083201 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='院区表';
```

## 功能特性

### 1. 院区列表管理
- 分页查询院区列表
- 支持按院区名称、地址搜索
- 支持按启用状态筛选
- 实时显示创建时间和更新时间

### 2. 院区信息维护
- **新增院区**：支持添加新的院区信息
- **编辑院区**：修改现有院区的基本信息
- **删除院区**：单个删除或批量删除院区
- **状态管理**：快速启用/禁用院区

### 3. 数据导入导出
- **批量导入**：支持 Excel 文件批量导入院区数据
- **数据导出**：导出院区列表为 Excel 文件

### 4. 表单验证
- 院区名称：必填，长度限制 1-128 字符
- 院区地址：必填，长度限制 1-255 字符
- 备注信息：可选，长度限制 255 字符
- 启用状态：必选，默认启用

## 文件结构

```
src/views/hospital/
├── index.vue                    # 院区管理主页面
├── index.scss                   # 页面样式文件
├── components/
│   └── HospitalDrawer.vue       # 院区新增/编辑抽屉组件
└── README.md                    # 功能说明文档

src/api/modules/
└── hospital.ts                  # 院区管理 API 接口

src/api/interface/
└── index.ts                     # 院区相关类型定义
```

## API 接口

### 院区列表查询
- **接口**：`GET /v1/web/hospital/paging`
- **参数**：分页参数 + 搜索条件

### 院区详情查询
- **接口**：`GET /v1/web/hospital/{id}`

### 新增院区
- **接口**：`POST /v1/web/hospital`

### 编辑院区
- **接口**：`PUT /v1/web/hospital`

### 删除院区
- **接口**：`PUT /v1/web/hospital/delete`

### 状态切换
- **接口**：`PUT /v1/web/hospital/status`

### 数据导出
- **接口**：`GET /v1/web/hospital/export`

### 批量导入
- **接口**：`POST /v1/web/hospital/import`

## 使用说明

1. **访问路径**：系统管理 -> 院区管理
2. **权限要求**：需要院区管理相关权限
3. **操作流程**：
   - 查看院区列表
   - 使用搜索功能筛选数据
   - 点击"新增院区"添加新院区
   - 点击"编辑"修改院区信息
   - 使用状态开关快速启用/禁用院区
   - 支持批量删除和数据导入导出

## 注意事项

1. 删除院区时请确认该院区没有关联的业务数据
2. 禁用院区后，相关业务功能可能受到影响
3. 批量导入时请使用标准的 Excel 模板格式
4. 院区名称在系统中建议保持唯一性
