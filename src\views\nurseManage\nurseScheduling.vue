<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="Download" @click="showExport()">导出</el-button>
      </template>
      <template #operation="scope">
        <el-button
          v-show="scope.row.status !== Nurse.WorkerStatus.Leave"
          type="primary"
          link
          :icon="Edit"
          @click="handleLeaveNurse(scope.row)"
          >修改为请假
        </el-button>
        <el-button
          v-show="scope.row.status === Nurse.WorkerStatus.Working"
          type="primary"
          link
          :icon="EditPen"
          @click="handleEditNurse(scope.row)"
          >设置替班
        </el-button>
        <el-button
          v-show="scope.row.status === Nurse.WorkerStatus.Leave"
          type="primary"
          link
          :icon="Suitcase"
          @click="handleCancelLeaveNurse(scope.row)"
          >修改为空闲
        </el-button>
      </template>
    </ProTable>
    <EditNurseDialog ref="nurseDialog" />
    <ExportNurseScheduling ref="exportDialog" />
  </div>
</template>

<script setup lang="tsx" name="NurseScheduling">
import dayjs from "dayjs";
import { Nurse, getNurseSchedule, WorkerStatusType, leaveNurse, cancelLeaveNurse } from "@/api/modules/nurseManage";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { Edit, EditPen, Download, Suitcase } from "@element-plus/icons-vue";
import { useHandleData } from "@/hooks/useHandleData";
import EditNurseDialog from "./components/EditNurseDialog.vue";
import ExportNurseScheduling from "./components/ExportNurseScheduling.vue";
import { ElTag } from "element-plus";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

/**
 * 查询列表参数
 */
const getTableList = (params: any) => {
  return getNurseSchedule(params);
};

const route = useRoute();

const columns = reactive<ColumnProps<Nurse.ResNurseSchedule>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  {
    prop: "id",
    label: "id",
    width: 180
  },
  {
    prop: "orderId",
    label: "陪护单号",
    search: { el: "input" },
    isShow: false
  },
  {
    prop: "date",
    label: "排班",
    width: 120,
    render: scope => <span>{dayjs(String(scope.row.date)).format("YYYY-MM-DD")}</span>
  },
  {
    prop: "nursingName",
    label: "护工名称",
    search: {
      el: "input"
    }
  },
  {
    prop: "nursingId",
    label: "护工工号",
    width: 180,
    search: {
      el: "input",
      defaultValue: route.query.nursingId ? String(route.query.nursingId) : ""
    }
  },
  {
    prop: "status",
    label: "班次状态",
    search: { el: "select" },
    enum: WorkerStatusType
  },
  {
    prop: "orderList",
    label: "关联陪护单",
    width: 260,
    render: scope =>
      scope.row.orderList ? (
        <div>
          {scope.row.orderList.map(item => (
            <ElTag class="ml-1">{item.orderId}</ElTag>
          ))}
        </div>
      ) : (
        "--"
      )
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);

const exportDialog = ref<InstanceType<typeof ExportNurseScheduling>>();
function showExport() {
  exportDialog.value?.acceptParams();
}

/**
 * 请假
 */
async function handleLeaveNurse(row: Nurse.ResNurseSchedule) {
  await useHandleData(leaveNurse, { id: row.id }, `将【${row.nursingName}】设为请假`);
  proTable.value?.getTableList();
}

/**
 * 修改为空闲
 */
async function handleCancelLeaveNurse(row: Nurse.ResNurseSchedule) {
  await useHandleData(cancelLeaveNurse, { id: row.id }, `将【${row.nursingName}】设为空闲`);
  proTable.value?.getTableList();
}

const nurseDialog = ref<InstanceType<typeof EditNurseDialog>>();
/**
 * 设置替班
 */
function handleEditNurse(row: Nurse.ResNurseSchedule) {
  nurseDialog.value?.acceptParams(row, proTable.value?.getTableList);
}
</script>
