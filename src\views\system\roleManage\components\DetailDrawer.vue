<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}角色`">
    <el-form
      ref="ruleFormRef"
      label-width="100px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <el-form-item label="角色姓名" prop="name">
        <el-input v-model="drawerProps.row!.name" placeholder="请填写用户姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="权限字符" prop="roleKey">
        <el-input v-model="drawerProps.row!.roleKey"></el-input>
      </el-form-item>
      <el-form-item label="角色状态" prop="status">
        <el-switch v-model="drawerProps.row!.status" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="菜单权限">
        <el-tree
          v-if="menuTree"
          :default-expand-all="true"
          node-key="id"
          ref="menuTreeRef"
          highlight-current
          :default-checked-keys="drawerProps.row!.menuIds"
          :data="menuTree"
          :props="{
            label: 'name',
            children: 'childrenList'
          }"
          show-checkbox
        />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="drawerProps.row!.remark" type="textarea" placeholder="请填写备注" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="DetailDrawer">
import { ref, reactive } from "vue";
import type { ElTree, FormInstance, FormRules } from "element-plus";
import { Role, setRoleMenu } from "@/api/modules/role";
import { getMenuTree } from "@/api/modules/menu";

const menuTreeRef = ref<InstanceType<typeof ElTree> | null>(null);

const rules = reactive<FormRules>({
  name: [{ required: true, message: "请填写角色名称" }],
  roleKey: [{ required: true, message: "请填写权限字符" }]
});

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<Role.ResRole>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
};

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  const tree = menuTreeRef.value!.getCheckedNodes(false, true);
  const menuIds = tree.map((item: any) => item.id);

  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const { data } = await drawerProps.value.api!(drawerProps.value.row);
      const roleId = drawerProps.value.row!.id || data.roleId;
      if (menuIds.length) {
        await setRoleMenu({
          roleId,
          menuIds
        });
      }
      ElMessage.success({ message: `${drawerProps.value.title}角色成功！` });
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

const { state: menuTree } = useAsyncState(async () => {
  const { data } = await getMenuTree();
  return data;
}, null);

defineExpose({
  acceptParams
});
</script>
