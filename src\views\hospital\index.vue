<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增院区</el-button>
        <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除
        </el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button type="primary" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </ProTable>
    <HospitalDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="hospitalManage">
import { Hospital } from "@/api/interface/index";
import { getHospitalList, deleteHospital, changeHospitalStatus } from "@/api/modules/hospital";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { CirclePlus, Delete, EditPen } from "@element-plus/icons-vue";
import HospitalDrawer from "./components/HospitalDrawer.vue";
import dayjs from "dayjs";
import { useHandleData } from "@/hooks/useHandleData";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

/**
 * 查询列表参数
 */
const getTableList = (params: any) => {
  return getHospitalList(params);
};

const renderTime = (time: string) => {
  return (
    <div>
      <span>{dayjs(time).format("YYYY-MM-DD HH:mm:ss")}</span>
    </div>
  );
};

const columns = reactive<ColumnProps<Hospital.ResHospitalList>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "name",
    label: "院区名称",
    search: { el: "input" }
  },
  {
    prop: "address",
    label: "院区地址",
    search: { el: "input" }
  },
  {
    prop: "remark",
    label: "备注",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "enable",
    label: "启用状态",
    render: scope => {
      return (
        <el-switch model-value={scope.row.enable} active-value={1} inactive-value={0} onChange={() => changeStatus(scope.row)} />
      );
    }
  },
  {
    prop: "createdAt",
    label: "创建时间",
    render: scope => renderTime(scope.row.createdAt)
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    render: scope => renderTime(scope.row.updatedAt)
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);

/**
 * 打开 drawer(新增、编辑)
 */
const drawerRef = ref<InstanceType<typeof HospitalDrawer> | null>(null);
async function openDrawer(title: string, row: Partial<Hospital.ResHospitalList> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: undefined,
    getTableList: proTable.value?.getTableList
  };
  drawerRef.value?.acceptParams(params);
}

/**
 * 删除院区信息
 */
const handleDelete = async (params: Hospital.ResHospitalList) => {
  await useHandleData(deleteHospital, [params.id], `删除【${params.name}】院区`);
  proTable.value?.getTableList();
};

/**
 * 批量删除院区信息
 */
async function batchDelete(selectedListIds: string[]) {
  await useHandleData(deleteHospital, selectedListIds, "删除所选院区信息");
  proTable.value?.getTableList();
}

/**
 * 切换院区状态
 */
async function changeStatus(row: Hospital.ResHospitalList) {
  const status = row.enable === 1 ? 0 : 1;
  const statusText = status === 1 ? "启用" : "禁用";
  await useHandleData(changeHospitalStatus, { id: row.id, enable: status }, `${statusText}【${row.name}】院区`);
  proTable.value?.getTableList();
}
</script>

<style scoped lang="scss">
@use "./index.scss";
</style>
