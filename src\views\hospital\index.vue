<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增院区</el-button>
        <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除
        </el-button>
        <el-button type="success" :icon="Upload" plain @click="openImportDialog">批量导入</el-button>
        <el-button type="info" :icon="Download" plain @click="exportData">导出数据</el-button>
      </template>
      <!-- 启用状态 -->
      <template #enable="scope">
        <el-switch
          :model-value="scope.row.enable"
          :active-value="1"
          :inactive-value="0"
          @change="changeStatus(scope.row)"
        />
      </template>
      <!-- 操作列 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button type="primary" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </ProTable>

    <!-- 新增/编辑抽屉 -->
    <HospitalDrawer ref="drawerRef" />
    
    <!-- 批量导入对话框 -->
    <ImportExcel ref="importRef" />
  </div>
</template>

<script setup lang="tsx" name="hospitalManage">
import { Hospital } from "@/api/interface/index";
import { 
  getHospitalList, 
  deleteHospital, 
  changeHospitalStatus,
  exportHospitalInfo,
  batchImportHospital
} from "@/api/modules/hospital";
import ProTable from "@/components/ProTable/index.vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { CirclePlus, Delete, EditPen, Upload, Download } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { useHandleData } from "@/hooks/useHandleData";
import { useDownload } from "@/hooks/useDownload";
import HospitalDrawer from "./components/HospitalDrawer.vue";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

/**
 * 查询列表参数
 */
const getTableList = (params: any) => {
  return getHospitalList(params);
};

/**
 * 渲染时间
 */
const renderTime = (time: string) => {
  return (
    <div>
      <span>{dayjs(time).format("YYYY-MM-DD HH:mm:ss")}</span>
    </div>
  );
};

/**
 * 表格列配置
 */
const columns = reactive<ColumnProps<Hospital.ResHospitalList>[]>([
  { type: "selection", fixed: "left", width: 70 },
  {
    prop: "name",
    label: "院区名称",
    search: { el: "input", tooltip: "请输入院区名称" }
  },
  {
    prop: "address",
    label: "院区地址",
    search: { el: "input", tooltip: "请输入院区地址" }
  },
  {
    prop: "remark",
    label: "备注",
    width: 200,
    showOverflowTooltip: true
  },
  {
    prop: "enable",
    label: "启用状态",
    width: 120,
    search: {
      el: "select",
      props: {
        filterable: false,
        options: [
          { label: "启用", value: 1 },
          { label: "禁用", value: 0 }
        ]
      }
    }
  },
  {
    prop: "createdAt",
    label: "创建时间",
    width: 180,
    render: scope => renderTime(scope.row.createdAt)
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    width: 180,
    render: scope => renderTime(scope.row.updatedAt)
  },
  {
    width: 200,
    prop: "operation",
    label: "操作",
    fixed: "right"
  }
]);

/**
 * 删除院区
 */
const handleDelete = async (params: Hospital.ResHospitalList) => {
  await useHandleData(deleteHospital, [params.id], `删除【${params.name}】院区`);
  proTable.value?.getTableList();
};

/**
 * 批量删除院区
 */
async function batchDelete(selectedListIds: string[]) {
  await useHandleData(deleteHospital, selectedListIds, "删除所选院区信息");
  proTable.value?.getTableList();
}

/**
 * 切换院区状态
 */
async function changeStatus(row: Hospital.ResHospitalList) {
  const status = row.enable === 1 ? 0 : 1;
  const statusText = status === 1 ? "启用" : "禁用";
  
  await useHandleData(
    changeHospitalStatus, 
    { id: row.id, enable: status }, 
    `${statusText}【${row.name}】院区`
  );
  
  proTable.value?.getTableList();
}

/**
 * 打开抽屉
 */
const drawerRef = ref<InstanceType<typeof HospitalDrawer>>();
const openDrawer = (title: string, row: Partial<Hospital.ResHospitalList> = {}) => {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: title === "新增" ? undefined : title === "编辑" ? row : undefined,
    getTableList: proTable.value?.getTableList
  };
  drawerRef.value?.acceptParams(params);
};

/**
 * 导出数据
 */
const exportData = async () => {
  useDownload(exportHospitalInfo, "院区数据", proTable.value?.searchParam);
};

/**
 * 批量导入
 */
const importRef = ref<InstanceType<typeof ImportExcel>>();
const openImportDialog = () => {
  const params = {
    title: "院区",
    fileType: ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],
    importApi: batchImportHospital,
    getTableList: proTable.value?.getTableList
  };
  importRef.value?.acceptParams(params);
};
</script>

<style scoped lang="scss">
@use "./index.scss";
</style>
