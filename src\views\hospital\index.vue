<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="院区列表"
      row-key="id"
      :columns="columns"
      :request-api="getHospitalList"
      :init-param="initParam"
      :data-callback="dataCallback"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增院区</el-button>
        <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除
        </el-button>
      </template>
      <!-- 表格搜索 -->
      <template #toolButton>
        <el-button type="primary" :icon="Refresh" circle @click="proTable?.getTableList()" />
      </template>
      <!-- 院区状态 -->
      <template #enable="scope">
        <el-switch :model-value="scope.row.enable" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)" />
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="View" @click="openDrawer('查看', scope.row)">查看</el-button>
        <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button type="primary" link :icon="Delete" @click="deleteAccount(scope.row)">删除</el-button>
      </template>
    </ProTable>
    <HospitalDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="hospitalManage">
import { reactive, ref } from "vue";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import HospitalDrawer from "./components/HospitalDrawer.vue";
import { CirclePlus, Delete, EditPen, View, Refresh } from "@element-plus/icons-vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import {
  Hospital,
  getHospitalList,
  addHospital,
  editHospital,
  deleteHospital,
  changeHospitalStatus
} from "@/api/modules/hospital";
import { ElMessage } from "element-plus";

// ProTable 实例
const proTable = ref<ProTableInstance>();

// 表格初始化请求参数
const initParam = reactive({
  name: "",
  enable: ""
});

// 表格配置项
const columns = reactive<ColumnProps<Hospital.ResHospitalList>[]>([
  { type: "selection", fixed: "left", width: 70 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "name",
    label: "院区名称",
    search: { el: "input", props: { placeholder: "请输入院区名称" } }
  },
  {
    prop: "address",
    label: "院区地址",
    width: 200
  },
  {
    prop: "remark",
    label: "备注",
    width: 200
  },
  {
    prop: "enable",
    label: "启用状态",
    width: 120,
    search: {
      el: "select",
      props: {
        placeholder: "请选择状态",
        clearable: true
      }
    },
    enum: [
      { label: "启用", value: 1 },
      { label: "禁用", value: 0 }
    ],
    fieldNames: { label: "label", value: "value" }
  },
  {
    prop: "createdAt",
    label: "创建时间",
    width: 180,
    sortable: true
  },
  { prop: "operation", label: "操作", width: 240, fixed: "right" }
]);

// 处理返回的数据格式
const dataCallback = (data: any) => {
  return {
    list: data.records,
    total: data.total,
    pageNum: data.current,
    pageSize: data.size
  };
};

// 删除院区信息
const deleteAccount = async (params: Hospital.ResHospitalList) => {
  await useHandleData(deleteHospital, [params.id], `删除【${params.name}】院区`);
  proTable.value?.getTableList();
};

// 批量删除院区
const batchDelete = async (ids: string[]) => {
  await useHandleData(deleteHospital, ids, "批量删除院区");
  proTable.value?.getTableList();
};

// 切换院区状态
const changeStatus = async (row: Hospital.ResHospitalList) => {
  try {
    const newStatus = row.enable === 1 ? 0 : 1;
    await changeHospitalStatus({ id: row.id, enable: newStatus });
    ElMessage.success(`${newStatus === 1 ? "启用" : "禁用"}成功`);
    proTable.value?.getTableList();
  } catch (error) {
    console.error("状态切换失败:", error);
  }
};

// 打开 drawer(新增、查看、编辑)
const drawerRef = ref<InstanceType<typeof HospitalDrawer> | null>(null);
const openDrawer = (title: string, row: Partial<Hospital.ReqHospitalParams> = {}) => {
  const params = {
    title,
    row: { ...row },
    isView: title === "查看",
    api: title === "新增" ? addHospital : title === "编辑" ? editHospital : undefined,
    getTableList: proTable.value?.getTableList
  };
  drawerRef.value?.acceptParams(params);
};
</script>

<style scoped lang="scss">
.table-box {
  width: 100%;
  height: 100%;
}
</style>