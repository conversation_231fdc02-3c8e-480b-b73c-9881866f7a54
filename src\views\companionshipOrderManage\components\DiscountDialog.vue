<template>
  <el-dialog title="请选择折扣" v-model="dialogVisible">
    <el-form ref="ruleFormRef" label-width="100px" label-suffix=" :" @submit="onSubmit">
      <el-form-item label="折扣" :error="errors.discount" required>
        <el-input v-bind="formData.discount">
          <template #append>%</template>
        </el-input>
      </el-form-item>
      <el-form-item label="折扣类型" :error="errors.type" required>
        <!-- :model-value="formData.type.modelValue"
          @update:model-value="formData.type['onUpdate:modelValue']"
          :validate-event="false" -->
        <!-- @vue-ignore -->
        <el-radio-group v-bind="formData.type">
          <el-radio v-for="item in discountTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useCompanionshipOrderStore, DialogType } from "@/stores/modules/companionshipOrder";
import { CompanionshipOrder, discountTypeOptions, setDiscount } from "@/api/modules/companionshipOrder";
import { toTypedSchema } from "@vee-validate/yup";
import { object, number } from "yup";
import { useForm } from "vee-validate";
import { MaxDigitsAfterDecimal3 } from "@/utils/eleValidate";
import { convertProportionToThousandth, roundPriceByThousand } from "@/utils";
const companionshipOrderStore = useCompanionshipOrderStore();

let orderId: string;

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    discount: number()
      .moreThan(0)
      .max(100)
      .transform(value => (isNaN(value) ? undefined : value))
      .required("请输入正确的折扣")
      .test("MaxDigitsAfterDecimal3", "折扣必须是一个正数，且最多保留三位小数", number =>
        MaxDigitsAfterDecimal3.test(number.toString())
      )
      .label("折扣")
  })
);

const { defineComponentBinds, handleSubmit, resetForm, errors, setValues } = useForm<{
  discount: string;
  type: number;
}>({
  validationSchema: schema,
  initialValues: {
    discount: "100",
    type: CompanionshipOrder.DiscountType.DoNotAffectExistingCosts
  }
});

const formData = ref({
  discount: defineComponentBinds("discount", elPlusConfig),
  type: defineComponentBinds("type", elPlusConfig)
});

const DIALOGTYPE = DialogType.Discount;
const dialogVisible = computed<boolean>({
  set(val) {
    if (val) {
      companionshipOrderStore.dialogInfo.dialogVisible = DIALOGTYPE;
    } else {
      companionshipOrderStore.dialogInfo.dialogVisible = DialogType.None;
    }
    resetForm();
  },
  get() {
    if (companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE) {
      orderId = companionshipOrderStore.dialogInfo.info!.id;
      setValues({
        discount: convertProportionToThousandth(companionshipOrderStore.dialogInfo.info!.discount)?.toString(),
        type: companionshipOrderStore.dialogInfo.info!.discountType
      });
    }
    return companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE;
  }
});

const onSubmit = handleSubmit(async values => {
  console.log(values);

  const params = {
    orderId: orderId,
    discount: roundPriceByThousand(values.discount)!,
    type: values.type
  };

  await setDiscount(params);
  ElMessage("修改成功");
  companionshipOrderStore.dialogInfo = {
    info: undefined,
    dialogVisible: DialogType.None
  };
  companionshipOrderStore.getTableList!();
});
</script>
