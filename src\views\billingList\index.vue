<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="requestApi">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <div class="flex justify-between items-center">
          <el-button class="mb-0! mr-4" type="primary" :icon="Download" @click="handleExport">导出</el-button>
          <div class="mr-4">应收金额：{{ `￥${priceFormat(priceReceived)}` }}</div>
          <div>未收金额：{{ `￥${priceFormat(priceUnReceived)}` }}</div>
        </div>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="View" @click="toDetail(scope.row.orderId)">查看账单明细</el-button>
        <el-button
          v-if="scope.row.payStatus === Bill.PayStatus.NotPay"
          type="primary"
          link
          :icon="Edit"
          @click="handleCashPay(scope.row)"
          >现金支付
        </el-button>
        <el-button
          v-if="scope.row.payStatus === Bill.PayStatus.NotPay"
          link
          type="primary"
          :icon="Money"
          @click="handleAlipay(scope.row)"
          >支付宝支付
        </el-button>
      </template>
    </ProTable>
    <AlipayDialog ref="alipayDialog" />
  </div>
</template>

<script setup lang="tsx" name="billingList">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { View, Edit, Download, Money } from "@element-plus/icons-vue";
import { useHandleData } from "@/hooks/useHandleData";
import {
  getBillListApi,
  getBillStatistics,
  Bill,
  billTypeOptions,
  billSubTypeOptions,
  tradeTypeOptions,
  payStatusOptions,
  cashPay,
  invoiceTypeOptions,
  needCertificateOptions,
  filterRefundBillOptions,
  exportBillDate
} from "@/api/modules/billing";
import { useTabsStore } from "@/stores/modules/tabs";
import { priceFormat } from "@/utils";
import { useDownload } from "@/hooks/useDownload";
import dayjs from "dayjs";
import AlipayDialog from "./components/AlipayDialog.vue";
import { deposit } from "@/api/modules/cashlog";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();
const route = useRoute();
const tabStore = useTabsStore();

const defaultOrderId = route.query.orderId && typeof route.query.orderId === "string" ? route.query.orderId : "";
if (defaultOrderId) {
  tabStore.setTabsTitle(`资金明细-${defaultOrderId}`);
}

const columns = reactive<ColumnProps<Bill.BillProperties>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  {
    prop: "orderId",
    label: "陪护单号",
    search: { el: "input", defaultValue: defaultOrderId },
    width: 180
  },
  {
    prop: "id",
    label: "账单编号",
    width: 180
  },
  {
    prop: "billType",
    label: "账单类型",
    tag: true,
    width: 110,
    enum: billTypeOptions
  },
  {
    prop: "billSubType",
    label: "周期账单类型",
    width: 110,
    tag: true,
    enum: billSubTypeOptions
  },
  {
    prop: "payStatus",
    label: "支付状态",
    tag: true,
    width: 110,
    enum: payStatusOptions
  },
  {
    prop: "filterRefundBill",
    label: "是否退款账单",
    isShow: false,
    search: { el: "select" },
    enum: filterRefundBillOptions
  },
  {
    prop: "price",
    label: "账单金额",
    width: 150,
    render: ({ row }) => {
      return <span class={{ "text-red": row.price < 0 }}>￥{priceFormat(row.price)}元</span>;
    }
  },
  {
    prop: "createTime",
    label: "账单时间",
    width: 180,
    search: { el: "date-picker", props: { type: "daterange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "patientName",
    label: "病人姓名",
    width: 110
  },
  {
    prop: "startTime",
    label: "账单开始时间",
    width: 180
  },
  {
    prop: "endTime",
    label: "账单结束时间",
    width: 180
  },
  {
    prop: "nursingList",
    label: "护工",
    width: 120,
    render: ({ row }) => {
      return <div>{row.nursingList?.length ? row.nursingList.map(item => <span class="ml-1">{item.name}</span>) : "--"}</div>;
    }
  },
  {
    prop: "tradeType",
    label: "账单支付方式",
    tag: true,
    width: 100,
    enum: tradeTypeOptions,
    search: { el: "select" }
  },
  {
    prop: "payTime",
    label: "账单支付时间",
    width: 140
  },
  {
    prop: "itemNames",
    width: 200,
    label: "账单包含服务名称"
  },
  {
    prop: "invoice.needCertificate",
    label: "付费凭证",
    width: 200,
    enum: needCertificateOptions
  },
  {
    prop: "invoice.invoiceType",
    label: "发票类型",
    width: 120,
    enum: invoiceTypeOptions
  },
  {
    prop: "invoice.email",
    label: "电子邮箱",
    width: 200
  },
  {
    prop: "invoice.taxpayerName",
    label: "纳税人名称",
    width: 200
  },
  {
    prop: "invoice.taxpayerNo",
    label: "纳税人识别号",
    width: 200
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);

const router = useRouter();
// 账单明细
function toDetail(id: number) {
  router.push({ name: "dailyProfitSharing", query: { orderId: id } });
}

// 现金支付
async function handleCashPay(row: Bill.BillProperties) {
  await useHandleData(cashPay, { billId: row.id, cashSource: 1 }, `切换已支付状态`);
  proTable.value?.getTableList();
}

function handleExport() {
  const searchParam = proTable.value?.searchParam;
  useDownload(exportBillDate, `账单导出-${dayjs().format("YYYYMMDD")}`, searchParam);
}

const alipayDialog = ref<InstanceType<typeof AlipayDialog>>();
async function handleAlipay(row: Bill.BillProperties) {
  await useHandleData(cashPay, { billId: row.id, cashSource: 3 }, `切换已支付状态`);
  proTable.value?.getTableList();
}

const priceReceived = ref("");
const priceUnReceived = ref("");

function requestApi(params: any) {
  getBillStatistics(params).then(res => {
    priceReceived.value = res.data.priceReceived;
    priceUnReceived.value = res.data.priceUnReceived;
  });
  return getBillListApi(params);
}
</script>
