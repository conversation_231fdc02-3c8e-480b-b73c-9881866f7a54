import { ReqPage, ResPage, User } from "@/api/interface/index";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

/**
 * @name 用户管理模块
 */
// 获取用户列表
export const getUserList = (params: ReqPage & Partial<User.ResUserList>, loading = true) => {
  return http.get<ResPage<User.ResUserList>>(PORT1 + `/account/paging`, params, { loading });
};

// 获取用户详情
export const getUserDetail = (id: string) => {
  return http.get<
    User.ResUserList & {
      departmentList?: User.UserOrg[];
      organizationList?: User.UserOrg[];
      roleList?: User.UserRole[];
      hospitalId?: string | number;
    }
  >(PORT1 + `/account/${id}`);
};

// // 获取树形用户列表
// export const getUserTreeList = (params: { name: string }) => {
//   return http.post<ResPage<User.ResUserList>>(PORT1 + `/user/tree/list`, params);
// };

// 新增用户
export const addUser = (params: User.ReqUserParams) => {
  return http.post<{ accountId: string }>(PORT1 + `/account`, params);
};

// // 批量添加用户
// export const BatchAddUser = (params: FormData) => {
//   return http.post(PORT1 + `/user/import`, params);
// };

// 编辑用户
export const editUser = (params: User.ReqUserParams) => {
  return http.put(PORT1 + `/account`, params);
};

// 删除用户
export const deleteUser = (ids: string[]) => {
  return http.put(PORT1 + `/account/delete`, { ids });
};
/**
 * 用户设置角色
 */
export const userSetRole = (params: { accountId: string; roleIds: string[] }) => {
  return http.post(PORT1 + "/account/allocation-role", params);
};

// 重置用户密码
export const resetUserPassWord = (params: { accountId: string; newPassword: string }) => {
  return http.put(PORT1 + `/account/change-password`, params);
};

// // 切换用户状态
// export const changeUserStatus = (params: { id: string; status: number }) => {
//   return http.post(PORT1 + `/user/change`, params);
// };

// // 导出用户数据
// export const exportUserInfo = (params: User.ReqUserParams) => {
//   return http.download(PORT1 + `/user/export`, params);
// };

// // 获取用户状态字典
// export const getUserStatus = () => {
//   return http.get<User.ResStatus[]>(PORT1 + `/user/status`);
// };

// // 获取用户性别字典
// export const getUserGender = () => {
//   return http.get<User.ResGender[]>(PORT1 + `/user/gender`);
// };

// // 获取用户部门列表
// export const getUserDepartment = () => {
//   return http.get<User.ResDepartment[]>(PORT1 + `/user/department`);
// };

// // 获取用户角色字典
// export const getUserRole = () => {
//   return http.get<User.ResRole[]>(PORT1 + `/user/role`);
// };
