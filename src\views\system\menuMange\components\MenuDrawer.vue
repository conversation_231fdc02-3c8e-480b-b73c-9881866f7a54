<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}菜单`">
    <el-form
      ref="ruleFormRef"
      label-width="100px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="drawerProps.row!.name" placeholder="请输入菜单名称" clearable maxlength="50" show-word-limit />
      </el-form-item>

      <el-form-item label="菜单标识" prop="frontName">
        <el-input v-model="drawerProps.row!.frontName" placeholder="请输入菜单标识" clearable maxlength="50" show-word-limit />
      </el-form-item>

      <el-form-item label="菜单类型" prop="type">
        <el-select v-model="drawerProps.row!.type" placeholder="请选择菜单类型" clearable>
          <el-option label="菜单页面" :value="1" />
          <el-option label="按钮权限" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="父级菜单" prop="parentId">
        <el-tree-select
          v-model="drawerProps.row!.parentId"
          :data="menuTreeData"
          :props="{ value: 'id', label: 'name', children: 'childrenList' }"
          placeholder="请选择父级菜单"
          clearable
          check-strictly
          :render-after-expand="false"
        />
      </el-form-item>

      <el-form-item label="组件路径" prop="componentUrl">
        <el-input
          v-model="drawerProps.row!.componentUrl"
          placeholder="请输入组件路径"
          clearable
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="菜单图标" prop="icon">
        <el-input v-model="drawerProps.row!.icon" placeholder="请输入图标名称" clearable maxlength="50" show-word-limit />
      </el-form-item>

      <el-form-item label="权限标识" prop="menuKey">
        <el-input v-model="drawerProps.row!.menuKey" placeholder="请输入权限标识" clearable maxlength="100" show-word-limit />
      </el-form-item>

      <el-form-item label="需要权限" prop="requiresAuth">
        <el-switch
          v-model="drawerProps.row!.requiresAuth"
          :active-value="true"
          :inactive-value="false"
          active-text="需要权限"
          inactive-text="无需权限"
        />
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="drawerProps.row!.sort" :min="0" :max="9999" placeholder="请输入排序" />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-switch
          v-model="drawerProps.row!.status"
          :active-value="1"
          :inactive-value="0"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="drawerProps.row!.remark"
          type="textarea"
          placeholder="请输入备注信息"
          clearable
          maxlength="255"
          show-word-limit
          :rows="4"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="MenuDrawer">
import { ref, reactive, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { addMenu, editMenu, getMenuTree } from "@/api/modules/menu";

/**
 * 表单验证规则
 */
const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入菜单名称", trigger: "blur" },
    { min: 1, max: 50, message: "菜单名称长度在 1 到 50 个字符", trigger: "blur" }
  ],
  frontName: [
    { required: true, message: "请输入菜单标识", trigger: "blur" },
    { min: 1, max: 50, message: "菜单标识长度在 1 到 50 个字符", trigger: "blur" }
  ],
  type: [{ required: true, message: "请选择菜单类型", trigger: "change" }],
  componentUrl: [
    { required: true, message: "请输入组件路径", trigger: "blur" },
    { min: 1, max: 200, message: "组件路径长度在 1 到 200 个字符", trigger: "blur" }
  ],
  menuKey: [{ max: 100, message: "权限标识长度不能超过 100 个字符", trigger: "blur" }],
  sort: [{ required: true, message: "请输入排序", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  requiresAuth: [{ required: true, message: "请选择是否需要权限", trigger: "change" }],
  remark: [{ max: 255, message: "备注长度不能超过 255 个字符", trigger: "blur" }]
});

/**
 * 抽屉属性接口
 */
interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<Menu.MenuResOptions>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

/**
 * 抽屉状态
 */
const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

/**
 * 菜单树数据
 */
const menuTreeData = ref<Menu.MenuResOptions[]>([]);

/**
 * 获取菜单树数据
 */
const getMenuTreeData = async () => {
  try {
    const { data } = await getMenuTree();
    menuTreeData.value = data;
  } catch (error) {
    console.log(error);
  }
};

/**
 * 接收父组件传过来的参数
 */
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;

  // 设置默认值
  if (drawerProps.value.title === "新增") {
    drawerProps.value.row = {
      name: "",
      frontName: "",
      type: 1,
      parentId: "",
      componentUrl: "",
      icon: "",
      menuKey: "",
      sort: 0,
      status: 1,
      requiresAuth: true,
      remark: ""
    };
  }

  drawerVisible.value = true;
};

/**
 * 提交数据（新增/编辑）
 */
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;

    try {
      const params: Menu.ReqMenuParams = {
        name: drawerProps.value.row!.name!,
        frontName: drawerProps.value.row!.frontName!,
        type: drawerProps.value.row!.type!,
        parentId: drawerProps.value.row!.parentId || "",
        componentUrl: drawerProps.value.row!.componentUrl!,
        icon: drawerProps.value.row!.icon || "",
        menuKey: drawerProps.value.row!.menuKey || "",
        sort: drawerProps.value.row!.sort || 0,
        status: drawerProps.value.row!.status,
        requiresAuth: drawerProps.value.row!.requiresAuth,
        remark: drawerProps.value.row!.remark || ""
      };

      // 如果是编辑，需要传递 id
      if (drawerProps.value.title === "编辑" && drawerProps.value.row!.id) {
        params.id = drawerProps.value.row!.id;
      }

      // 调用对应的 API
      const api = drawerProps.value.title === "新增" ? addMenu : editMenu;
      await api(params);

      ElMessage.success({ message: `${drawerProps.value.title}菜单成功！` });
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

/**
 * 组件挂载时获取菜单树数据
 */
onMounted(() => {
  getMenuTreeData();
});

/**
 * 暴露给父组件的方法
 */
defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-switch {
    --el-switch-on-color: var(--el-color-primary);
    --el-switch-off-color: var(--el-color-info);
  }
}
</style>
