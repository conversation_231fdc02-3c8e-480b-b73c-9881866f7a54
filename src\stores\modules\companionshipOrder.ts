import { CompanionshipOrder } from "@/api/modules/companionshipOrder";
import { defineStore } from "pinia";

interface DrawerProps {
  title: string;
  isView: boolean;
  row?: CompanionshipOrder.Record;
}

export enum DialogType {
  None,
  /**
   * 设置护工
   */
  Nurse,
  /**
   * 付费方式
   */
  PayType,
  /**
   * 设置套餐
   */
  ServerPackage,
  /**
   * 设置折扣
   */
  Discount,
  /**
   * 设置分成比例
   */
  ShareRatio
}

interface DialogInfo {
  dialogVisible?: DialogType;
  info?: { [key: string]: any } | undefined;
}

export const useCompanionshipOrderStore = defineStore("companionshipOrder", () => {
  const getTableList = ref<() => Promise<void>>();

  // 弹窗
  const dialogInfo = ref<DialogInfo>({});

  /**
   * @description：抽屉参数
   */
  const drawerParams = ref<DrawerProps | undefined>();
  /**
   * 抽屉开关
   */
  const drawerVisible = ref(false);

  function openDrawer(params: DrawerProps) {
    drawerParams.value = params;
    drawerVisible.value = true;
  }

  return { getTableList, drawerParams, drawerVisible, openDrawer, dialogInfo };
});
