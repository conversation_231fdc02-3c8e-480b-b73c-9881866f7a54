<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getDeviceList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">增加设备</el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Edit" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button type="primary" link :icon="Download" @click="handleDownloadQRCode(scope.row)">生成二维码</el-button>
      </template>
    </ProTable>
    <canvas class="hidden" ref="canvasRef"></canvas>
    <DetailDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="dailyProfitSharing">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { Device, getDeviceList, systemTypeOptions, statusOptions, addDevice, editDevice } from "@/api/modules/device";
import { CirclePlus, Edit, Download } from "@element-plus/icons-vue";
import DetailDrawer from "./components/DetailDrawer.vue";
import QRCode from "qrcode";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<Device.EquipmentRes>[]>([
  {
    prop: "code",
    label: "设备编号",
    width: 200,
    search: { el: "input" }
  },
  {
    prop: "name",
    label: "设备名称",
    search: { el: "input" }
  },
  {
    prop: "systemType",
    label: "归属系统",
    enum: systemTypeOptions,
    tag: true
  },
  {
    prop: "orgName",
    label: "所属院区"
  },
  {
    prop: "orgDepartmentName",
    label: "所属科室"
  },
  {
    prop: "status",
    label: "设备状态",
    enum: statusOptions,
    tag: true
  },
  {
    prop: "address",
    label: "设备地址"
  },
  {
    prop: "remark",
    label: "备注"
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);
/**
 * 打开 drawer(新增、查看、编辑)
 */
const drawerRef = ref<InstanceType<typeof DetailDrawer> | null>(null);
async function openDrawer(title: "查看" | "新增" | "编辑", row: Partial<Device.EquipmentRes> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: title === "新增" ? addDevice : title === "编辑" ? editDevice : undefined,
    getTableList: proTable.value?.getTableList
  };
  if (title !== "新增" && row.id) {
    // const res = await getDeviceDetail(row.id);
    params.row = row;
  }

  drawerRef.value?.acceptParams(params);
}

const canvasRef = ref<HTMLCanvasElement | null>(null);
async function generateQRCode(text: string) {
  await QRCode.toCanvas(canvasRef.value, text);
}

function downloadQRCode(fileName: string = "qrcode") {
  const image = canvasRef.value!.toDataURL("image/png").replace("image/png", "image/octet-stream");
  const link = document.createElement("a");
  link.download = `${fileName}.png`;
  link.href = image;
  link.click();
}

const qrcodeText = "https://engineering-fe.rxwysystem.com/pages/repair/index?device_id=";
// 生成并下载二维码
async function handleDownloadQRCode(row: Partial<Device.EquipmentRes>) {
  await generateQRCode(qrcodeText + row.id);
  downloadQRCode(row.id?.toString());
}
</script>
