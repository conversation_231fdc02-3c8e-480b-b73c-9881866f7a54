import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage } from "../interface";
import { StatusEnum } from "@/utils/dict";

export namespace Org {
  export interface ResOrganization {
    id: string;
    name: string;
    parentId: string;
    liaisonName: string;
    liaisonPhoneNumber: string;
    bankAccountNumber: string;
    level: orgType;
    type: number;
    status: StatusEnum;
    sort: number;
  }
  export interface ResOrganizationTree extends ResOrganization {
    childrenList: Org.ResOrganizationTree[];
  }

  export enum orgType {
    /**
     * 医院
     */
    hospital = 1,
    /**
     * 部门
     */
    department = 2
  }

  export interface ReqOrganizationParams extends ReqPage {
    id?: string;
    name: string;
    type: orgType;
    parentId?: string;
    liaisonName?: string;
    liaisonPhoneNumber?: string;
    bankAccountNumber?: string;
    status: StatusEnum;
    sort: number;
  }
}

export const orgTypeOptions = [
  { label: "医院", value: Org.orgType.hospital },
  { label: "部门", value: Org.orgType.department }
];

/**
 * 查询组织机构树形结构
 */
export const getOrganizationTree = () => {
  return http.get<Org.ResOrganizationTree[]>(PORT1 + "/organization/account-tree", {}, { loading: true });
};

/**
 *  查询医院列表
 */
export const getOrganizationList = () => {
  return http.get<Org.ResOrganization[]>(PORT1 + "/organization/hospital-organization-list", {}, { loading: false });
};

/**
 * 查询物业部门列表
 */
export const getDepartmentList = () => {
  return http.get<Org.ResOrganization[]>(PORT1 + "/organization/department-organization-list", {}, { loading: false });
};

/**
 * 查询医院下的部门列表
 * @param hospitalOrganizationId 医院id
 */
export const getDepartmentListByHospitalId = (hospitalOrganizationId: string) => {
  return http.get<Org.ResOrganization[]>(
    PORT1 + "/organization/administrative-office-organization-list",
    { hospitalOrganizationId },
    { loading: false }
  );
};

/**
 * 添加组织机构
 */
export const addOrganization = (params: Org.ReqOrganizationParams) => {
  return http.post(PORT1 + "/organization", params, { loading: true });
};

/**
 * 修改组织机构
 */
export const editOrganization = (params: Partial<Org.ReqOrganizationParams>) => {
  return http.put(PORT1 + "/organization", params, { loading: true });
};

/**
 * 删除组织架构
 */
export const deleteOrganization = (ids: string[]) => {
  return http.put(PORT1 + `/organization/delete`, { ids }, { loading: true });
};
