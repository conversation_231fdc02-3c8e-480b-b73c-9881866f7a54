<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" title="附件下载">
    <div>
      <div v-for="(item, index) in drawerProps.attachments" :key="index">
        <el-link class="mb-4" type="primary" :href="item.url" :download="item.name" icon="download">{{ item.name }}</el-link>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts" name="SparePartsDetailDrawer">
interface DrawerProps {
  attachments: {
    name: string;
    url: string;
  }[];
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  attachments: []
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
};

defineExpose({
  acceptParams
});
</script>
