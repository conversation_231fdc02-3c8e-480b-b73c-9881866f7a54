<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}应急处理日志`">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit" :disabled="drawerProps.isView">
      <el-form-item :error="errors.title" label="标题" required>
        <el-input v-bind="formData.title" />
      </el-form-item>
      <el-form-item :error="errors.eventTime" label="入库时间" required>
        <el-date-picker v-bind="formData.eventTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
      </el-form-item>
      <el-form-item :error="errors.orgId" label="医院" required>
        <el-select v-bind="formData.orgId">
          <el-option v-for="item in orgList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.remark" label="备注">
        <el-input v-bind="formData.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="onSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="SparePartsDetailDrawer">
import { useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string } from "yup";
import { useOrgStore } from "@/stores/modules/orgId";
import { Emergency } from "@/api/modules/emergency";

interface DrawerProps {
  title?: "新增" | "编辑" | "查看";
  isView: boolean;
  row: Partial<Emergency.LogRes>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    title: string().required().label("标题"),
    eventTime: string().required().label("时间"),
    remark: string().label("备注"),
    orgId: string().required().label("医院")
  })
);

const { defineComponentBinds, handleSubmit, errors, setValues, resetForm } = useForm<Emergency.LogParams>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  title: defineComponentBinds("title", elPlusConfig),
  eventTime: defineComponentBinds("eventTime", elPlusConfig),
  orgId: defineComponentBinds("orgId", elPlusConfig),
  remark: defineComponentBinds("remark", elPlusConfig)
});

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: undefined,
  row: {}
});

/**
 * 医院列表
 */
const orgStore = useOrgStore();
const orgList = toRef(orgStore, "hospitalList");

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  if (params.title !== "新增") {
    setValues(params.row);
  } else {
    resetForm();
  }

  drawerVisible.value = true;
};

const onSubmit = handleSubmit(async values => {
  await drawerProps.value.api!(values);
  drawerVisible.value = false;
  drawerProps.value.getTableList!();
});

defineExpose({
  acceptParams
});
</script>
