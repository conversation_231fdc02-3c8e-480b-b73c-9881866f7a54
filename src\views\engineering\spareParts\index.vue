<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getSparePartsBatch">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">增加备品备件</el-button>
        <el-button type="primary" :icon="CirclePlus" @click="goCreateApproval">创建入库申请</el-button>
        <el-button type="primary" @click="goApprovalList">入库审批</el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Edit" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button type="primary" link :icon="View" @click="goQuantityDetails(scope.row)">查看数量明细</el-button>
      </template>
    </ProTable>
    <DetailDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="dailyProfitSharing">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { addSpareParts, editSpareParts, getSparePartsBatch, getSparePartsDetail, SpareParts } from "@/api/modules/spareParts";
import { CirclePlus, View, Edit } from "@element-plus/icons-vue";
import DetailDrawer from "./components/DetailDrawer.vue";
import { priceFormat } from "@/utils";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<SpareParts.PartsRes>[]>([
  {
    prop: "code",
    label: "备品备件编号",
    width: 200,
    search: { el: "input" }
  },
  {
    prop: "name",
    label: "备品备件名称",
    search: { el: "input" }
  },
  {
    prop: "price",
    label: "单价",
    render: ({ row }) => {
      return <span>￥{priceFormat(row.price)}</span>;
    }
  },
  {
    prop: "balance",
    label: "当前可用数量"
  },
  {
    prop: "unit",
    label: "单位名称"
  },
  {
    prop: "remark",
    label: "说明"
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);
/**
 * 打开 drawer(新增、查看、编辑)
 */
const drawerRef = ref<InstanceType<typeof DetailDrawer> | null>(null);
async function openDrawer(title: "查看" | "新增" | "编辑", row: Partial<SpareParts.PartsRes> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: title === "新增" ? addSpareParts : title === "编辑" ? editSpareParts : undefined,
    getTableList: proTable.value?.getTableList
  };
  if (title !== "新增" && row.id) {
    const res = await getSparePartsDetail(row.id);
    params.row = res.data;
  }
  drawerRef.value?.acceptParams(params);
}

const router = useRouter();
/**
 * 跳转数量明细
 */
function goQuantityDetails(row: SpareParts.PartsRes) {
  router.push({
    name: "sparePartsQuantityDetails",
    params: {
      id: row.id
    }
  });
}

function goCreateApproval() {
  router.push({ name: "createInboundApproval" });
}

function goApprovalList() {
  router.push({ name: "inboundApprovalList" });
}
</script>
