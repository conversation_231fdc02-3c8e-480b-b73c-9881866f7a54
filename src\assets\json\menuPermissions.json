[{"id": "1", "name": "系统管理", "frontName": "system", "parentId": null, "componentUrl": "/system", "icon": "Tools", "menuKey": "system", "type": 1, "status": 1, "sort": 100, "requiresAuth": true, "remark": "系统管理模块", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": [{"id": "101", "name": "账号管理", "frontName": "accountManage", "parentId": "1", "componentUrl": "/system/accountManage/index", "icon": "User", "menuKey": "system:account", "type": 1, "status": 1, "sort": 1, "requiresAuth": true, "remark": "账号管理页面", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": [{"id": "10101", "name": "新增账号", "frontName": "accountAdd", "parentId": "101", "componentUrl": "", "icon": "", "menuKey": "system:account:add", "type": 2, "status": 1, "sort": 1, "requiresAuth": true, "remark": "新增账号按钮权限", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}, {"id": "10102", "name": "编辑账号", "frontName": "accountEdit", "parentId": "101", "componentUrl": "", "icon": "", "menuKey": "system:account:edit", "type": 2, "status": 1, "sort": 2, "requiresAuth": true, "remark": "编辑账号按钮权限", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}, {"id": "10103", "name": "删除账号", "frontName": "accountDelete", "parentId": "101", "componentUrl": "", "icon": "", "menuKey": "system:account:delete", "type": 2, "status": 1, "sort": 3, "requiresAuth": true, "remark": "删除账号按钮权限", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}]}, {"id": "102", "name": "角色管理", "frontName": "roleManage", "parentId": "1", "componentUrl": "/system/roleManage/index", "icon": "<PERSON><PERSON>", "menuKey": "system:role", "type": 1, "status": 1, "sort": 2, "requiresAuth": true, "remark": "角色管理页面", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}, {"id": "103", "name": "菜单管理", "frontName": "menuManage", "parentId": "1", "componentUrl": "/system/menuMange/index", "icon": "Operation", "menuKey": "system:menu", "type": 1, "status": 1, "sort": 3, "requiresAuth": true, "remark": "菜单管理页面", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}, {"id": "104", "name": "组织机构管理", "frontName": "departmentManage", "parentId": "1", "componentUrl": "/system/departmentManage/index", "icon": "Suitcase", "menuKey": "system:department", "type": 1, "status": 1, "sort": 4, "requiresAuth": true, "remark": "组织机构管理页面", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}, {"id": "105", "name": "院区管理", "frontName": "hospitalManage", "parentId": "1", "componentUrl": "/hospital/index", "icon": "OfficeBuilding", "menuKey": "system:hospital", "type": 1, "status": 1, "sort": 5, "requiresAuth": true, "remark": "院区管理页面", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": [{"id": "10501", "name": "新增院区", "frontName": "hospitalAdd", "parentId": "105", "componentUrl": "", "icon": "", "menuKey": "system:hospital:add", "type": 2, "status": 1, "sort": 1, "requiresAuth": true, "remark": "新增院区按钮权限", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}, {"id": "10502", "name": "编辑院区", "frontName": "hospitalEdit", "parentId": "105", "componentUrl": "", "icon": "", "menuKey": "system:hospital:edit", "type": 2, "status": 1, "sort": 2, "requiresAuth": true, "remark": "编辑院区按钮权限", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}, {"id": "10503", "name": "删除院区", "frontName": "hospitalDelete", "parentId": "105", "componentUrl": "", "icon": "", "menuKey": "system:hospital:delete", "type": 2, "status": 1, "sort": 3, "requiresAuth": true, "remark": "删除院区按钮权限", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}, {"id": "10504", "name": "院区状态切换", "frontName": "hospitalStatus", "parentId": "105", "componentUrl": "", "icon": "", "menuKey": "system:hospital:status", "type": 2, "status": 1, "sort": 4, "requiresAuth": true, "remark": "院区状态切换按钮权限", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}]}]}, {"id": "2", "name": "首页", "frontName": "home", "parentId": null, "componentUrl": "/home/<USER>", "icon": "HomeFilled", "menuKey": "home", "type": 1, "status": 1, "sort": 1, "requiresAuth": false, "remark": "系统首页，无需权限验证", "createBy": "admin", "updateBy": "admin", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00", "childrenList": []}]