<template>
  <el-dialog :title="`支付宝${dialogProps?.title}`" v-model="dialogVisible">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit">
      <el-form-item :error="errors.price" :label="`${dialogProps?.title}金额`" required>
        <el-input v-bind="formData.price" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { toTypedSchema } from "@vee-validate/yup";
import { object, string } from "yup";
import { useForm } from "vee-validate";
import { priceToCent } from "@/utils";

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    price: string()
      .matches(/^(?:\d+(\.\d{1,2})?)?$/, "价格必须大于零，且最多保留两位小数")
      .required("请输入支付金额")
      .label("支付金额")
  })
);

const { defineComponentBinds, handleSubmit, errors, resetForm } = useForm<{
  price?: string;
}>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  price: defineComponentBinds("price", elPlusConfig)
});

interface DialogProps {
  getTableList: () => void;
  title: string;
  api: (params: any) => Promise<any>;
}

const dialogVisible = ref(false);
const dialogProps = ref<DialogProps>();
// 接收父组件传过来的参数
const acceptParams = (params: DialogProps) => {
  dialogVisible.value = true;
  resetForm();
  dialogProps.value = params;
};

// TODO ID 提交
const onSubmit = handleSubmit(async values => {
  await dialogProps.value?.api({
    cashSource: 1,
    price: priceToCent(values.price)
  });
  dialogVisible.value = false;
  dialogProps.value?.getTableList();
});

defineExpose({
  acceptParams
});
</script>
