<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getApprovalList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="goCreate"> 创建入库申请 </el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Edit" @click="goEdit(scope.row)">编辑</el-button>
        <el-button v-if="scope.row.attachments?.length" type="primary" link :icon="View" @click="openDrawer(scope.row)">
          附件下载
        </el-button>
        <el-button
          v-if="scope.row.status === SpareParts.ApprovalStatusEnum.Pending"
          type="primary"
          link
          :icon="View"
          @click="handleAudit(scope.row, SpareParts.ApprovalStatusEnum.Passed)"
          >通过
        </el-button>
        <el-button
          v-if="scope.row.status === SpareParts.ApprovalStatusEnum.Pending"
          type="primary"
          link
          :icon="View"
          @click="handleAudit(scope.row, SpareParts.ApprovalStatusEnum.Rejected)"
          >驳回
        </el-button>
      </template>
    </ProTable>
    <AttachmentDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="inboundApprovalList">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { ApprovalStatusOptions, InvestorTypeOptions, SpareParts, auditApproval, getApprovalList } from "@/api/modules/spareParts";
import { CirclePlus, View, Edit } from "@element-plus/icons-vue";
import AttachmentDrawer from "./components/attachmentDrawer.vue";
import { useHandleData } from "@/hooks/useHandleData";
import dayjs from "dayjs";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<SpareParts.ApprovalRes>[]>([
  {
    prop: "id",
    label: "审批单号",
    width: 200
  },
  {
    prop: "name",
    label: "物品名称（数量）"
  },
  {
    prop: "investorType",
    label: "出资方",
    enum: InvestorTypeOptions
  },
  {
    prop: "investor",
    label: "出资方名称"
  },
  {
    prop: "entryTime",
    label: "入库时间",
    render: scope => <span>{scope.row.entryTime ? dayjs(scope.row.entryTime).format("YYYY-MM-DD HH:mm:ss") : "--"}</span>
  },
  {
    prop: "operator",
    label: "操作人"
  },
  {
    prop: "status",
    label: "审批单状态",
    enum: ApprovalStatusOptions,
    tag: true
  },
  {
    prop: "operation",
    label: "操作",
    width: 350,
    fixed: "right"
  }
]);
/**
 * 打开 drawer(新增、查看、编辑)
 */
const drawerRef = ref<InstanceType<typeof AttachmentDrawer> | null>(null);
async function openDrawer(row: SpareParts.ApprovalRes) {
  const params = {
    attachments: row.attachments!
  };
  drawerRef.value?.acceptParams(params);
}

const router = useRouter();

function goCreate() {
  router.push({ name: "createInboundApproval" });
}
/**
 * 跳转明细
 */
// function goDetails(row: SpareParts.PartsRes) {
//   router.push({
//     name: "viewInboundApproval",
//     params: {
//       id: row.id
//     }
//   });
// }

function goEdit(row: SpareParts.PartsRes) {
  router.push({
    name: "editInboundApproval",
    params: {
      id: row.id
    }
  });
}

async function handleAudit(row: SpareParts.PartsRes, status: SpareParts.ApprovalStatusEnum) {
  const msg = status === SpareParts.ApprovalStatusEnum.Rejected ? `驳回审批单${row.id}` : `通过审批单${row.id}`;

  await useHandleData(
    auditApproval,
    {
      id: row.id,
      status
    },
    msg
  );
  proTable.value?.getTableList();
}
</script>
