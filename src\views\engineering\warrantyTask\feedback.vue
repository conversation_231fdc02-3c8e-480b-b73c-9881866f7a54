<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="requestApi">
      <!-- <template #expand="scope">
        <el-row class="pl-10" :gutter="20">
          <el-col :span="12" v-if="scope.row.part && scope.row.part.length">
            <div class="mb-1">备件信息：</div>
            <div v-for="(item, index) in scope.row.part" :key="index">{{ `${item.name}: ${item.quantity}件` }}</div>
          </el-col>
          <el-col :span="12" v-if="scope.row.attachments && scope.row.attachments.length">
            <div class="mb-1">图片说明：</div>
            <el-image
              v-for="(item, index) in scope.row.attachments"
              :key="index"
              style="width: 100px; height: 100px"
              :src="item"
              :preview-src-list="scope.row.attachments"
              fit="cover"
            />
          </el-col>
        </el-row>
      </template> -->
    </ProTable>
    <DetailDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="warrantyTaskFeedback">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { systemTypeOptions } from "@/api/modules/device";
import { feedbackTypeOptions, WarrantyTask, getWarrantyTaskFeedback } from "@/api/modules/warrantyTask";
import dayjs from "dayjs";
import DetailDrawer from "./components/DetailDrawer.vue";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<WarrantyTask.FeedbackRes>[]>([
  {
    prop: "code",
    label: "报修单编号",
    width: 200
  },
  // { type: "expand", label: "展开", width: 85 },
  {
    prop: "systemType",
    label: "报修单类型",
    enum: systemTypeOptions,
    tag: true
  },
  {
    prop: "createByName",
    label: "反馈人员"
  },
  {
    prop: "createAt",
    label: "反馈时间",
    render: ({ row }) => {
      return dayjs(row.createAt).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  {
    prop: "feedbackType",
    label: "反馈结果类型",
    tag: true,
    enum: feedbackTypeOptions
  },
  {
    prop: "remark",
    label: "情况说明"
  },
  {
    prop: "attachments",
    label: "图片说明",
    width: 200,
    showOverflowTooltip: false,
    render: scope => {
      const imgs = scope.row.attachments.split(",");
      return (
        <div>
          {imgs.map((img, index) => (
            <el-image
              class="ml-1"
              initialIndex={index}
              preview-teleported
              src={img + "?x-oss-process=image/resize,w_100,m_lfit"}
              previewSrcList={imgs}
              z-index={99999}
              fit="scale-down"
            />
          ))}
        </div>
      );
    }
  },
  {
    prop: "part",
    label: "备品备件",
    render: scope => {
      return scope.row.part?.length ? scope.row.part.map(item => `${item.name}: ${item.quantity}件`).join("、") : "--";
    }
  }
  // {
  //   prop: "",
  //   label: "审核结果"
  // }
  // {
  //   prop: "operation",
  //   label: "操作",
  //   width: 250,
  //   fixed: "right"
  // }
]);
const route = useRoute();

function requestApi(params: any) {
  if (route.params.id && typeof route.params.id === "string") {
    params.repairFormId = route.params.id;
  }
  return getWarrantyTaskFeedback(params);
}
</script>
