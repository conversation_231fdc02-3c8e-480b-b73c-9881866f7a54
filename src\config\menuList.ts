const menuList: Menu.MenuOptions[] = [
  {
    path: "/home/<USER>",
    name: "home",
    component: "/home/<USER>",
    meta: {
      icon: "HomeFilled",
      title: "首页",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: true,
      isKeepAlive: true
    }
  },
  {
    path: "/home/<USER>",
    name: "zfile",
    meta: {
      icon: "Folder",
      title: "网盘",
      isLink: "/zfile",
      isHide: false,
      isFull: false,
      isAffix: true,
      isKeepAlive: false
    }
  },
  {
    path: "/service-package",
    name: "servicePackageManage",
    component: "/servicePackage/index",
    meta: {
      icon: "ShoppingTrolley",
      title: "服务套餐",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/companionship-order-manage",
    name: "companionshipOrderManage",
    redirect: "/companionship-order-manage/index",
    component: "/companionshipOrderManage/index",
    meta: {
      icon: "Document",
      title: "陪护单管理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/companionship-order-manage/index",
        name: "companionshipOrderManage",
        component: "/companionshipOrderManage/index",
        meta: {
          icon: "Document",
          title: "陪护单管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/companionship-order-manage/hospital-manage",
        name: "companionshipOrderHospitalManage",
        component: "/companionshipOrderManage/index",
        meta: {
          icon: "Document",
          title: "陪护单管理-查看",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/companionship-order-manage/modify-history",
        name: "companionshipOrderModifyHistory",
        component: "/companionshipOrderManage/modifyHistory",
        meta: {
          icon: "Files",
          title: "陪护单修改记录",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  },
  {
    path: "/order-evaluation",
    name: "orderEvaluation",
    component: "/orderEvaluation/index",
    meta: {
      icon: "ChatLineSquare",
      title: "订单评价",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/billing-list",
    name: "billingList",
    component: "/billingList/index",
    meta: {
      icon: "Memo",
      title: "账单列表",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/cash-archiving",
    name: "cashArchiving",
    component: "/cashArchiving/index",
    meta: {
      icon: "Money",
      title: "现金归档",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/dailyProfitSharing",
    name: "dailyProfitSharing",
    component: "/billingList/detail",
    meta: {
      icon: "PieChart",
      title: "每日分成",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/nurse-manage",
    name: "nurse",
    redirect: "/nurse-manage/index",
    meta: {
      icon: "User",
      title: "护工",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/nurse-manage/index",
        name: "nurseManage",
        component: "/nurseManage/index",
        meta: {
          icon: "User",
          title: "护工管理",
          activeMenu: "",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/nurse-manage/add",
        name: "nurseAdd",
        component: "/nurseManage/detail",
        meta: {
          icon: "User",
          title: "添加护工",
          activeMenu: "/nurse-manage",
          isLink: "",
          isHide: true,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/nurse-manage/view/:id",
        name: "nurseDetail",
        component: "/nurseManage/detail",
        meta: {
          icon: "User",
          title: "护工详情",
          activeMenu: "/nurse-manage",
          isLink: "",
          isHide: true,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/nurse-manage/edit/:id",
        name: "nurseEdit",
        component: "/nurseManage/detail",
        meta: {
          icon: "User",
          title: "编辑护工",
          activeMenu: "/nurse-manage",
          isLink: "",
          isHide: true,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/nurse-manage/nurse-scheduling",
        name: "nurseScheduling",
        component: "/nurseManage/nurseScheduling",
        meta: {
          icon: "Calendar",
          title: "护工排班信息",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  },
  {
    path: "/device",
    name: "deviceManagement",
    component: "/engineering/deviceManagement/index",
    meta: {
      icon: "Odometer",
      title: "设备管理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/spare-parts",
    name: "spareParts",
    redirect: "/spare-parts/index",
    meta: {
      icon: "Files",
      title: "备件管理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/spare-parts/index",
        name: "sparePartsManagement",
        component: "/engineering/spareParts/index",
        meta: {
          icon: "Files",
          title: "备件管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/spare-parts/quantity-details/:id",
        name: "sparePartsQuantityDetails",
        component: "/engineering/spareParts/sparePartsQuantityDetails",
        meta: {
          icon: "Files",
          title: "备件数量明细",
          isLink: "",
          isHide: true,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/spare-parts/inbound-approval-list",
        name: "inboundApprovalList",
        component: "/engineering/spareParts/inboundApprovalList",
        meta: {
          icon: "List",
          title: "入库审批管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/spare-parts/inbound-approval/create",
        name: "createInboundApproval",
        component: "/engineering/spareParts/inboundApproval",
        meta: {
          icon: "DocumentChecked",
          title: "入库审批",
          isLink: "",
          isHide: true,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/spare-parts/inbound-approval/edit/:id",
        name: "editInboundApproval",
        component: "/engineering/spareParts/inboundApproval",
        meta: {
          icon: "DocumentChecked",
          title: "编辑审批",
          isLink: "",
          isHide: true,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/spare-parts/inbound-approval/:id",
        name: "viewInboundApproval",
        component: "/engineering/spareParts/inboundApproval",
        meta: {
          icon: "DocumentChecked",
          title: "查看审批",
          isLink: "",
          isHide: true,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  },
  {
    path: "patrol",
    name: "planPatrol",
    redirect: "/plan/patrol",
    meta: {
      icon: "Checked",
      title: "巡检计划",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/plan/patrol",
        name: "planPatrol",
        component: "/engineering/planPatrol/index",
        meta: {
          icon: "Checked",
          title: "巡检计划",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/plan/patrol/tasklist",
        name: "patrolTaskList",
        component: "/engineering/planPatrol/taskList",
        meta: {
          icon: "Odometer",
          title: "任务明细",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  },
  {
    path: "maintenance",
    name: "maintenance",
    redirect: "/plan/maintenance",
    meta: {
      icon: "List",
      title: "维保任务",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/plan/maintenance",
        name: "planMaintenance",
        component: "/engineering/planPatrol/index",
        meta: {
          icon: "List",
          title: "维保任务",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/plan/maintenance/tasklist",
        name: "maintenanceTaskList",
        component: "/engineering/planPatrol/taskList",
        meta: {
          icon: "Odometer",
          title: "任务明细",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  },
  {
    path: "/emergency-handling-log",
    name: "emergencyHandlingLog",
    component: "/engineering/emergencyHandlingLog/index",
    meta: {
      icon: "Notebook",
      title: "应急处置日志",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    }
  },
  {
    path: "/warranty-task",
    name: "warrantyTask",
    redirect: "/warranty-task/index",
    meta: {
      icon: "Setting",
      title: "报修任务管理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/warranty-task/index",
        name: "warrantyTask",
        component: "/engineering/warrantyTask/index",
        meta: {
          icon: "Setting",
          title: "报修任务管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/warranty-task/detail/:id",
        name: "warrantyTaskFeedback",
        component: "/engineering/warrantyTask/feedback",
        meta: {
          icon: "Setting",
          title: "报修反馈信息",
          isLink: "",
          isHide: true,
          isFull: false,
          activeMenu: "/warranty-task/index",
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/warranty-task/spare-parts-use-approval",
        name: "sparePartsUseApproval",
        component: "/engineering/warrantyTask/sparePartsUseApproval",
        meta: {
          icon: "DocumentChecked",
          title: "备件使用审核",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  },
  {
    path: "/system",
    name: "system",
    redirect: "/system/account-manage",
    meta: {
      icon: "Tools",
      title: "系统管理",
      isLink: "",
      isHide: false,
      isFull: false,
      isAffix: false,
      isKeepAlive: true
    },
    children: [
      {
        path: "/system/account-manage",
        name: "accountManage",
        component: "/system/accountManage/index",
        meta: {
          icon: "User",
          title: "账号管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        },
        children: [
          {
            path: "/system/account-manage/add",
            name: "accountAdd",
            component: "/system/accountManage/detail",
            meta: {
              icon: "User",
              title: "添加用户",
              activeMenu: "/assembly/tabs",
              isLink: "",
              isHide: true,
              isFull: false,
              isAffix: false,
              isKeepAlive: true
            }
          },
          {
            path: "/system/account-manage/view/:id",
            name: "accountDetail",
            component: "/system/accountManage/detail",
            meta: {
              icon: "User",
              title: "用户详情",
              activeMenu: "/assembly/tabs",
              isLink: "",
              isHide: true,
              isFull: false,
              isAffix: false,
              isKeepAlive: true
            }
          },
          {
            path: "/system/account-manage/edit/:id",
            name: "accountEdit",
            component: "/system/accountManage/detail",
            meta: {
              icon: "User",
              title: "编辑用户",
              activeMenu: "/assembly/tabs",
              isLink: "",
              isHide: true,
              isFull: false,
              isAffix: false,
              isKeepAlive: true
            }
          }
        ]
      },
      {
        path: "/system/roleManage",
        name: "roleManage",
        component: "/system/roleManage/index",
        meta: {
          icon: "Menu",
          title: "角色管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/system/department-manage",
        name: "departmentManage",
        component: "/system/departmentManage/index",
        meta: {
          icon: "Suitcase",
          title: "组织机构管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      },
      {
        path: "/system/hospital-manage",
        name: "hospitalManage",
        component: "/hospital/index",
        meta: {
          icon: "OfficeBuilding",
          title: "院区管理",
          isLink: "",
          isHide: false,
          isFull: false,
          isAffix: false,
          isKeepAlive: true
        }
      }
    ]
  }
];

export default menuList;
