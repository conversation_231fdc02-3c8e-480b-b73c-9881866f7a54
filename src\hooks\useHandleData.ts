import { HandleData } from "./interface";

/**
 * @description 操作单条数据信息 (二次确认&#8203;``【oaicite:0】``&#8203;)
 * @param {Function} api 操作数据接口的api方法 (必传)
 * @param {Object} params 携带的操作数据参数 {id,params} (必传)
 * @param {String} message 提示信息 (必传)
 * @param {String} confirmType icon类型 (不必传,默认为 warning)
 * @returns {Promise<boolean>} 执行结果的Promise，如果操作成功，返回true，否则返回false。
 */
export const useHandleData = async <T>(
  api: (params: T) => Promise<any>,
  params: T,
  message: string,
  confirmType: HandleData.MessageType = "warning"
): Promise<boolean> => {
  try {
    await ElMessageBox.confirm(`是否${message}?`, "温馨提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: confirmType,
      draggable: true
    });

    const res = await api(params);
    if (!res) throw new Error(`操作失败`);

    ElMessage({
      type: "success",
      message: `${message}成功!`
    });

    return true;
  } catch (error) {
    if (error === "cancel") return false;
    ElMessage({
      type: "error",
      message: error instanceof Error ? error.message : "操作失败"
    });
    return false;
  }
};
