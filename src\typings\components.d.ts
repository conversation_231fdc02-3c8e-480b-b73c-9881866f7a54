/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    403: typeof import('./../components/ErrorMessage/403.vue')['default']
    404: typeof import('./../components/ErrorMessage/404.vue')['default']
    500: typeof import('./../components/ErrorMessage/500.vue')['default']
    ColSetting: typeof import('./../components/ProTable/components/ColSetting.vue')['default']
    ECharts: typeof import('./../components/ECharts/index.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Files: typeof import('./../components/Upload/Files.vue')['default']
    Grid: typeof import('./../components/Grid/index.vue')['default']
    GridItem: typeof import('./../components/Grid/components/GridItem.vue')['default']
    Img: typeof import('./../components/Upload/Img.vue')['default']
    Imgs: typeof import('./../components/Upload/Imgs.vue')['default']
    ImportExcel: typeof import('./../components/ImportExcel/index.vue')['default']
    Loading: typeof import('./../components/Loading/index.vue')['default']
    Pagination: typeof import('./../components/ProTable/components/Pagination.vue')['default']
    ProTable: typeof import('./../components/ProTable/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./../components/SearchForm/index.vue')['default']
    SearchFormItem: typeof import('./../components/SearchForm/components/SearchFormItem.vue')['default']
    SelectFilter: typeof import('./../components/SelectFilter/index.vue')['default']
    SelectIcon: typeof import('./../components/SelectIcon/index.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    SwitchDark: typeof import('./../components/SwitchDark/index.vue')['default']
    TableColumn: typeof import('./../components/ProTable/components/TableColumn.vue')['default']
    TreeFilter: typeof import('./../components/TreeFilter/index.vue')['default']
    WangEditor: typeof import('./../components/WangEditor/index.vue')['default']
  }
}
