<template>
  <div class="table-box">
    <ProTable ref="proTable" title="菜单列表" row-key="id" :indent="20" :columns="columns" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="openDrawer('新增')">新增菜单</el-button>
        <el-button type="danger" :icon="Delete" plain :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          批量删除
        </el-button>
      </template>
      <!-- 菜单图标 -->
      <template #icon="scope">
        <el-icon :size="18">
          <component :is="scope.row.icon"></component>
        </el-icon>
      </template>
      <!-- 菜单操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="EditPen" @click="openDrawer('编辑', scope.row)">编辑</el-button>
        <el-button type="primary" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
      </template>
    </ProTable>
    <MenuDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="tsx" name="menuMange">
import { ref, reactive } from "vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { Delete, EditPen, CirclePlus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { getMenuList, deleteMenu } from "@/api/modules/menu";
import { useHandleData } from "@/hooks/useHandleData";
import dayjs from "dayjs";
import MenuDrawer from "./components/MenuDrawer.vue";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

/**
 * 查询列表参数
 */
const getTableList = (params: any) => {
  return getMenuList(params);
};

/**
 * 渲染时间
 */
const renderTime = (time: string) => {
  return (
    <div>
      <span>{dayjs(time).format("YYYY-MM-DD HH:mm:ss")}</span>
    </div>
  );
};

// 表格配置项
const columns = reactive<ColumnProps<Menu.MenuResOptions>[]>([
  { type: "selection", fixed: "left", width: 70 },
  { prop: "name", label: "菜单名称", align: "left", search: { el: "input" } },
  { prop: "icon", label: "菜单图标", width: 100 },
  { prop: "frontName", label: "菜单标识", search: { el: "input" } },
  {
    prop: "type",
    label: "菜单类型",
    width: 100,
    search: {
      el: "select",
      props: {
        filterable: false,
        options: [
          { label: "菜单页面", value: 1 },
          { label: "按钮权限", value: 2 }
        ]
      }
    },
    render: scope => {
      return scope.row.type === 1 ? <el-tag type="primary">菜单页面</el-tag> : <el-tag type="warning">按钮权限</el-tag>;
    }
  },
  { prop: "componentUrl", label: "组件路径", width: 300 },
  { prop: "menuKey", label: "权限标识", width: 150 },
  {
    prop: "requiresAuth",
    label: "需要权限",
    width: 100,
    search: {
      el: "select",
      props: {
        filterable: false,
        options: [
          { label: "需要", value: true },
          { label: "不需要", value: false }
        ]
      }
    },
    render: scope => {
      return scope.row.requiresAuth ? <el-tag type="danger">需要权限</el-tag> : <el-tag type="success">无需权限</el-tag>;
    }
  },
  { prop: "sort", label: "排序", width: 80 },
  {
    prop: "status",
    label: "状态",
    width: 100,
    render: scope => {
      return scope.row.status === 1 ? <el-tag type="success">启用</el-tag> : <el-tag type="danger">禁用</el-tag>;
    }
  },
  {
    prop: "createdAt",
    label: "创建时间",
    width: 180,
    render: scope => renderTime(scope.row.createdAt)
  },
  { prop: "operation", label: "操作", width: 250, fixed: "right" }
]);

/**
 * 打开 drawer(新增、编辑)
 */
const drawerRef = ref<InstanceType<typeof MenuDrawer> | null>(null);
async function openDrawer(title: string, row: Partial<Menu.MenuResOptions> = {}) {
  const params = {
    title,
    isView: title === "查看",
    row: { ...row },
    api: undefined,
    getTableList: proTable.value?.getTableList
  };
  drawerRef.value?.acceptParams(params);
}

/**
 * 删除菜单信息
 */
const handleDelete = async (params: Menu.MenuResOptions) => {
  await useHandleData(deleteMenu, [params.id], `删除【${params.name}】菜单`);
  proTable.value?.getTableList();
};

/**
 * 批量删除菜单信息
 */
async function batchDelete(selectedListIds: string[]) {
  await useHandleData(deleteMenu, selectedListIds, "删除所选菜单信息");
  proTable.value?.getTableList();
}
</script>

<style scoped lang="scss">
.table-box {
  width: 100%;
  height: 100%;

  .el-button {
    margin-right: 10px;
  }
}
</style>
