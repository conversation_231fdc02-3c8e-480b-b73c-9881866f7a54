<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getOrderEvaluation"></ProTable>
  </div>
</template>

<script setup lang="tsx" name="dailyProfitSharing">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { getOrderEvaluation, Evaluation, sourceOptions } from "@/api/modules/orderEvaluation";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<Evaluation.Record>[]>([
  {
    prop: "orderId",
    label: "陪护单号",
    width: 200,
    search: { el: "input" }
  },
  {
    prop: "level",
    label: "评价等级",
    width: 150
  },
  {
    prop: "remark",
    label: "评价内容",
    width: 200
  },
  {
    prop: "source",
    label: "评价方",
    enum: sourceOptions,
    tag: true,
    search: { el: "select" }
  }
]);
</script>
