import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";

export namespace Device {
  export enum SystemType {
    /**
     * 水系统
     */
    WaterSystem = 1,
    /**
     * 强电系统
     */
    PowerSystem = 2,
    /**
     * 弱电系统
     */
    WeakPowerSystem = 3,
    /**
     * 气路系统
     */
    GasPathSystem = 4,
    /**
     * 工程质量投诉
     */
    EngineeringQualityComplaint = 5,
    /**
     * 其他
     */
    Other = 6,
    /**
     * 暖通系统
     */
    HVACSystem = 7,
    /**
     * 建筑系统
     */
    BuildingSystem = 8,
    /**
     * 消防系统
     */
    FireProtectionSystem = 9
  }

  export enum Status {
    /**
     * 启用
     */
    Enabled = 1,
    /**
     * 停用
     */
    Disabled = 2,
    /**
     * 维修中
     */
    Maintenance = 3
  }

  export interface EquipmentRes {
    /**
     * ID
     */
    id: number;
    /**
     * 编号
     */
    code: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 所属科室
     */
    orgDepartmentId: string;
    /**
     * 所属科室名称
     */
    orgDepartmentName: string;
    /**
     * 所属院区
     */
    orgId: string;
    /**
     * 所属院区名称
     */
    orgName: string;
    /**
     * 状态（1-启用，2-停用，3-维修中）
     */
    status: Status;
    /**
     * 地址
     */
    address: string;
    /**
     * 备注
     */
    remark: string;
    /**
     * 归属系统类型（1-水系统,2-强电系统,3-弱电系统,4-气路系统,5-工程质量投诉,6-其他）
     */
    systemType: SystemType;
  }
}

export const systemTypeOptions = [
  { value: Device.SystemType.WaterSystem, label: "水系统" },
  { value: Device.SystemType.PowerSystem, label: "强电系统" },
  { value: Device.SystemType.WeakPowerSystem, label: "弱电系统" },
  { value: Device.SystemType.GasPathSystem, label: "气路系统" },
  { value: Device.SystemType.EngineeringQualityComplaint, label: "工程质量投诉" },
  { value: Device.SystemType.Other, label: "其他" },
  { value: Device.SystemType.HVACSystem, label: "暖通系统" },
  { value: Device.SystemType.BuildingSystem, label: "建筑系统" },
  { value: Device.SystemType.FireProtectionSystem, label: "消防系统" }
];

export const statusOptions = [
  { value: Device.Status.Enabled, label: "启用" },
  { value: Device.Status.Disabled, label: "停用" },
  { value: Device.Status.Maintenance, label: "维修中" }
];

/**
 * @name 设备列表
 */
export const getDeviceList = (
  params: ReqPage & { code?: string; orgId?: string; name?: string; systemType?: Device.SystemType },
  loading = true
) => {
  return http.get<ResPage<Device.EquipmentRes>>(PORT1 + `/project/device/paging`, params, { loading });
};

/**
 * @name 新增设备
 */
export const addDevice = (params: Device.EquipmentRes, loading = true) => {
  return http.post(PORT1 + `/project/device`, params, { loading });
};

/**
 * @name 编辑设备
 */
export const editDevice = (params: Device.EquipmentRes & { id: string }, loading = true) => {
  return http.put(PORT1 + `/project/device`, params, { loading });
};
