<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="requestApi">
      <template #tableHeader>
        <div class="flex justify-between items-center">
          <div class="mr-4">{{ balance !== null ? `当前现金余额：￥${priceFormat(balance.price)}` : "" }}</div>
          <div>{{ balance !== null ? `支付宝支付金额：￥${priceFormat(balance.alipayPrice)}` : "" }}</div>
          <!-- <div>当前现金余额：￥100</div> -->
          <el-button class="mb-0! ml-4" type="primary" :icon="Money" @click="handleExtract">现金提取</el-button>
          <el-button class="mb-0! ml-4" type="primary" :icon="Money" @click="handleDeposit">现金存入</el-button>
        </div>
      </template>
    </ProTable>
    <ExtractDialog ref="extractDialog" />
  </div>
</template>

<script setup lang="tsx" name="cashArchiving">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { Money } from "@element-plus/icons-vue";
import {
  getCashlogList,
  Cashlog,
  operationTypeOptions,
  businessTypeOptions,
  getBalance,
  extract,
  deposit
} from "@/api/modules/cashlog";
import ExtractDialog from "./components/ExtractDialog.vue";
import { priceFormat } from "@/utils";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<Cashlog.Record>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  {
    prop: "type",
    label: "余额变动类型",
    enum: operationTypeOptions
  },
  {
    prop: "bizType",
    label: "业务类型",
    enum: businessTypeOptions,
    width: 180
  },
  {
    prop: "price",
    label: "变动金额",
    width: 180,
    render: ({ row }) => `￥${priceFormat(row.price)}`
  },
  {
    prop: "createAt",
    label: "变动时间",
    width: 200
  },
  {
    prop: "bizId",
    label: "关联账单编号",
    width: 200
  },
  {
    prop: "personName",
    label: "关联提取人"
  }
]);

const { state: balance, execute: refreshBalance } = useAsyncState(
  async () => {
    const { data } = await getBalance();
    return data;
  },
  null,
  {
    immediate: false
  }
);

const requestApi = async (params: any) => {
  refreshBalance();
  return getCashlogList(params);
};

const extractDialog = ref<InstanceType<typeof ExtractDialog>>();
// 现金提取
const handleExtract = () => {
  const params = {
    getTableList: proTable.value!.getTableList,
    title: "提取",
    api: extract
  };
  extractDialog.value?.acceptParams(params);
};

// 现金存入
const handleDeposit = () => {
  const params = {
    getTableList: proTable.value!.getTableList,
    title: "存入",
    api: deposit
  };
  extractDialog.value?.acceptParams(params);
};
</script>
