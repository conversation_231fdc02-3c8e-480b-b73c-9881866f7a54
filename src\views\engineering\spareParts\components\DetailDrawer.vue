<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}备件`">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit" :disabled="drawerProps.isView">
      <!-- <el-form-item label="备品备件编码" required>
        <el-input :value="drawerProps.row?.code" />
      </el-form-item> -->
      <el-form-item :error="errors.name" label="备品备件名称" required>
        <el-input v-bind="formData.name" />
      </el-form-item>
      <el-form-item label="当前可用数量" v-show="drawerProps.title !== '新增'">
        <!-- @vue-ignore -->
        <el-input-number v-bind="formData.quantity" disabled />
      </el-form-item>
      <el-form-item :error="errors.price" label="单价" required>
        <!-- @vue-ignore -->
        <el-input v-bind="formData.price" />
      </el-form-item>
      <el-form-item :error="errors.unit" label="单位" required>
        <el-input v-bind="formData.unit" />
      </el-form-item>
      <el-form-item :error="errors.remark" label="备注">
        <el-input v-bind="formData.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="onSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="SparePartsDetailDrawer">
import { useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string, number } from "yup";
import { SpareParts } from "@/api/modules/spareParts";
import { MaxDigitsAfterDecimal } from "@/utils/eleValidate";
import { priceFormat, priceToCent } from "@/utils";

interface DrawerProps {
  title?: "新增" | "编辑" | "查看";
  isView: boolean;
  row: Partial<SpareParts.PartParams & { code: string }>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    name: string().required().label("备品备件名称"),
    quantity: number().label("当前可用数量"),
    price: number()
      .min(0)
      .transform(value => (isNaN(value) ? undefined : value))
      .required("请输入正确的单价")
      .test("maxDigitsAfterDecimal", "价格不能小于零，且最多保留两位小数", number =>
        MaxDigitsAfterDecimal.test(number.toString())
      )
      .label("单价"),
    unit: string().required().label("单位"),
    remark: string().nullable().label("备注")
  })
);

const { defineComponentBinds, handleSubmit, errors, setValues, resetForm } = useForm<SpareParts.PartParams>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  name: defineComponentBinds("name", elPlusConfig),
  quantity: defineComponentBinds("quantity", () => ({
    props: {
      min: 0,
      precision: 0,
      validateEvent: false
    }
  })),
  price: defineComponentBinds("price", elPlusConfig),
  unit: defineComponentBinds("unit", elPlusConfig),
  remark: defineComponentBinds("remark", elPlusConfig)
});

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: undefined,
  row: {}
});
let id: string | undefined;
// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  if (params.title !== "新增") {
    setValues({
      name: params.row.name,
      quantity: params.row.quantity,
      price: priceFormat(params.row.price),
      unit: params.row.unit,
      remark: params.row.remark
    });
    id = params.row.id;
  } else {
    resetForm();
    id = undefined;
  }

  drawerVisible.value = true;
};

const onSubmit = handleSubmit(async values => {
  const params: any = {
    name: values.name,
    unit: values.unit,
    remark: values.remark,
    price: priceToCent(values.price)
  };
  if (id) {
    params.id = id;
  }

  await drawerProps.value.api!(params);
  drawerVisible.value = false;
  drawerProps.value.getTableList!();
});

defineExpose({
  acceptParams
});
</script>
