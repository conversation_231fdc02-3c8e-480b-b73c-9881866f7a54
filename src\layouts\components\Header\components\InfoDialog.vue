<template>
  <el-dialog v-model="dialogVisible" title="个人信息" width="500px" draggable>
    <div>
      <p>用户名: {{ nickname }}</p>
      <p>手机号: {{ phone }}</p>
      <!-- <p>最后登录时间: </p> -->
      <p>账号创建时间: {{ dayjs(createdAt).format("YYYY年MM月DD日 HH:mm:ss") }}</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useUserStore } from "@/stores/modules/user";

const userStore = useUserStore();

const nickname = toRef(userStore.userInfo, "nickname");
const phone = toRef(userStore.userInfo, "phoneNumber");
// const lastLoginDate = toRef(userStore.userInfo, "lastLoginDate");
const createdAt = toRef(userStore.userInfo, "createdAt");

const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
};

defineExpose({ openDialog });
</script>
