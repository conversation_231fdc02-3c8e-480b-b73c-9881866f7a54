<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="toDetail('nurseAdd')">新增护工</el-button>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="View" @click="toDetail('nurseDetail', scope.row)">查看</el-button>
        <el-button type="primary" link :icon="EditPen" @click="toDetail('nurseEdit', scope.row)">编辑</el-button>
        <el-button
          v-if="scope.row.status === Nurse.NursingStatus.Inactive"
          type="primary"
          link
          :icon="Delete"
          @click="handleDelete(scope.row)"
          >删除
        </el-button>
        <el-button type="primary" link :icon="View" @click="toNurseScheduling(scope.row)">查看排班信息</el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="NurseManage">
import { Nurse, getNurseList, deleteNurse, settleWayType, editNurse } from "@/api/modules/nurseManage";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { CirclePlus, EditPen, View, Delete } from "@element-plus/icons-vue";
import { useHandleData } from "@/hooks/useHandleData";
import { genderType } from "@/utils/dict";

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

/**
 * 查询列表参数
 */
const getTableList = (params: any) => {
  return getNurseList(params);
};

const columns = reactive<ColumnProps<Nurse.ResNurse>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  {
    prop: "id",
    label: "编号",
    width: 100
  },
  {
    prop: "name",
    label: "姓名",
    width: 160,
    search: { el: "input" }
  },
  // {
  //   prop: "idCardPic",
  //   label: "照片"
  // },
  {
    prop: "gender",
    label: "性别",
    enum: genderType
  },
  {
    prop: "age",
    label: "年龄"
  },
  {
    prop: "workYear",
    label: "服务年限",
    width: 120
  },
  {
    prop: "status",
    label: "状态",
    render: scope => (
      <el-switch
        model-value={scope.row.status}
        active-value={Nurse.NursingStatus.Active}
        inactive-value={Nurse.NursingStatus.Inactive}
        onChange={() => changeStatus(scope.row)}
      />
    )
  },
  {
    prop: "mobile",
    label: "联系方式",
    width: 120
  },
  {
    prop: "hospitalList",
    label: "所属院区",
    width: 250,
    render(scope) {
      return (
        <div>
          {scope.row.hospitalList.map(item => (
            <div>
              <el-tag class="mb-1">{item.name}</el-tag>
              <br />
            </div>
          ))}
        </div>
      );
    }
  },
  {
    prop: "settleWay",
    label: "费用结算方式",
    enum: settleWayType,
    tag: true,
    width: 120
  },
  {
    prop: "operation",
    label: "操作",
    width: 250,
    fixed: "right"
  }
]);

const router = useRouter();
function toDetail(name: string, row?: Nurse.ResNurse) {
  router.push({ name, params: { id: row?.id } });
}

/**
 * 查看排班信息
 */
function toNurseScheduling(row: Nurse.ResNurse) {
  router.push({ name: "nurseScheduling", query: { nursingId: row.id } });
}

/**
 * 删除护工信息
 */
const handleDelete = async (params: Nurse.ResNurse) => {
  await useHandleData(deleteNurse, params.id, `删除【${params.name}】护工`);
  proTable.value?.getTableList();
};

/**
 * 批量删除护工信息
 */
// async function batchDelete(selectedListIds: string[]) {
//   await useHandleData(deleteNurse, selectedListIds, "删除所选护工信息");
//   proTable.value?.getTableList();
// }

/**
 * 禁用启用护工
 * @param row
 */
async function changeStatus(row: Nurse.ResNurse) {
  if (row.status === Nurse.NursingStatus.Active) {
    await useHandleData(
      editNurse,
      { id: row.id, name: row.name, status: Nurse.NursingStatus.Inactive },
      `禁用【${row.name}】护工`
    );
  } else {
    await useHandleData(editNurse, { id: row.id, name: row.name, status: Nurse.NursingStatus.Active }, `启用【${row.name}】护工`);
  }
  proTable.value?.getTableList();
}
</script>
