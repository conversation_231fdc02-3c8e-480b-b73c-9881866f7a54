<template>
  <div class="main-box">
    <div class="table-box">
      <div class="card table-main">
        <div class="table-header">
          <div class="header-button-lf">
            <h4>院区数据调试页面</h4>
          </div>
        </div>
        
        <div class="debug-section">
          <h5>1. 院区数据加载测试</h5>
          <el-button @click="loadHospitalData" type="primary">加载院区数据</el-button>
          <div class="mt-2">
            <strong>院区数据状态：</strong>
            <span v-if="hospitalLoading">加载中...</span>
            <span v-else-if="hospitalList.length > 0" class="text-green-600">已加载 {{ hospitalList.length }} 条数据</span>
            <span v-else class="text-red-600">暂无数据</span>
          </div>
          <div class="mt-1" v-if="hospitalList.length > 0">
            <strong>第一条数据ID类型：</strong>{{ typeof hospitalList[0].id }} (值: {{ hospitalList[0].id }})
          </div>
          <pre class="debug-data">{{ JSON.stringify(hospitalList, null, 2) }}</pre>
        </div>
        
        <div class="debug-section">
          <h5>2. 用户详情模拟测试</h5>
          <el-input v-model="testUserId" placeholder="输入用户ID进行测试" class="w-64" />
          <el-button @click="loadUserData" type="primary" class="ml-2">加载用户数据</el-button>
          <div class="mt-2">
            <strong>用户数据状态：</strong>
            <span v-if="userLoading">加载中...</span>
            <span v-else-if="userData">已加载</span>
            <span v-else class="text-red-600">暂无数据</span>
          </div>
          <pre class="debug-data">{{ JSON.stringify(userData, null, 2) }}</pre>
        </div>
        
        <div class="debug-section">
          <h5>3. 院区选择器测试</h5>
          <el-select
            v-model="selectedHospitalId"
            placeholder="请选择院区"
            clearable
            class="w-64"
          >
            <el-option
              v-for="item in hospitalList"
              :key="item.id"
              :value="String(item.id)"
              :label="item.name"
            />
          </el-select>
          <div class="mt-2">
            <strong>选中的院区ID：</strong>{{ selectedHospitalId }}
          </div>
          <div class="mt-1">
            <strong>选中的院区名称：</strong>{{ getHospitalName(selectedHospitalId) }}
          </div>
        </div>
        
        <div class="debug-section">
          <h5>4. 数据匹配测试</h5>
          <div>
            <strong>模拟用户hospitalId：</strong>
            <el-input v-model="mockHospitalId" placeholder="输入hospitalId" class="w-64" />
          </div>
          <div class="mt-2">
            <strong>匹配的院区名称：</strong>{{ getHospitalName(mockHospitalId) }}
          </div>
          <div class="mt-1">
            <strong>是否在院区列表中：</strong>
            <span :class="isHospitalExists(mockHospitalId) ? 'text-green-600' : 'text-red-600'">
              {{ isHospitalExists(mockHospitalId) ? '存在' : '不存在' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="HospitalDebug">
import { ref, onMounted } from "vue";
import { Hospital } from "@/api/interface";
import { getResourceData } from "@/api/modules/hospital";
import { ElMessage } from "element-plus";

// 院区数据
const hospitalList = ref<Hospital.ResResourceData[]>([]);
const hospitalLoading = ref(false);

// 用户数据
const userData = ref<any>(null);
const userLoading = ref(false);
const testUserId = ref("2118756913864474624");

// 选择器测试
const selectedHospitalId = ref("");

// 数据匹配测试
const mockHospitalId = ref("2118541856262012928");

/**
 * 加载院区数据
 */
async function loadHospitalData() {
  hospitalLoading.value = true;
  try {
    const { data } = await getResourceData();
    hospitalList.value = data;
    console.log("院区数据加载成功:", data);
    ElMessage.success(`成功加载 ${data.length} 条院区数据`);
  } catch (error) {
    console.error("院区数据加载失败:", error);
    ElMessage.error("院区数据加载失败");
  } finally {
    hospitalLoading.value = false;
  }
}

/**
 * 模拟加载用户数据
 */
async function loadUserData() {
  userLoading.value = true;
  try {
    // 模拟用户数据
    const mockData = {
      id: testUserId.value,
      nickname: "张三",
      phoneNumber: "18888888882",
      hospitalId: "2118541856262012928",
      organizationList: [],
      departmentList: [
        {
          id: "354957228585091072",
          name: "部门-杭州第七人民医院陪护部"
        }
      ]
    };
    
    userData.value = mockData;
    console.log("用户数据:", mockData);
    ElMessage.success("用户数据加载成功");
  } catch (error) {
    console.error("用户数据加载失败:", error);
    ElMessage.error("用户数据加载失败");
  } finally {
    userLoading.value = false;
  }
}

/**
 * 根据ID获取院区名称
 */
function getHospitalName(hospitalId: string): string {
  if (!hospitalId) return "";
  const hospital = hospitalList.value.find(item => item.id === hospitalId);
  return hospital ? hospital.name : `未找到ID为 ${hospitalId} 的院区`;
}

/**
 * 检查院区是否存在
 */
function isHospitalExists(hospitalId: string): boolean {
  if (!hospitalId) return false;
  return hospitalList.value.some(item => item.id === hospitalId);
}

onMounted(() => {
  loadHospitalData();
});
</script>

<style scoped>
.main-box {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.table-box {
  flex: 1;
  overflow: hidden;
  border-radius: 12px;
}

.card {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--el-border-radius-base);
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.debug-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.debug-section h5 {
  margin: 0 0 15px 0;
  color: #409eff;
  font-weight: 600;
}

.debug-data {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 10px;
}

.text-green-600 {
  color: #16a34a;
}

.text-red-600 {
  color: #dc2626;
}

.w-64 {
  width: 16rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}
</style>
