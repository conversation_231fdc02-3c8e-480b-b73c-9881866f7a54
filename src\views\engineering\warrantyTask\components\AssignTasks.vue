<template>
  <el-dialog title="请设置人员" v-model="dialogVisible">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit">
      <el-form-item :error="errors.executorAccountIdList" label="人员" required>
        <el-select
          v-bind="formData.executorAccountIdList"
          value-key="id"
          reserve-keyword
          multiple
          filterable
          remote
          :remote-method="(name: string) => refreshExecutorList(500, name)"
          :loading="executorListLoading"
        >
          <el-option v-for="item in executorList" :key="item.id" :value="item.id" :label="item.nickname"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.urgencyDegreeType" label="紧急程度" required>
        <el-select v-bind="formData.urgencyDegreeType">
          <el-option v-for="item in urgencyDegreeOptions" :key="item.value" v-bind="item">{{ item.label }}</el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="onSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { toTypedSchema } from "@vee-validate/yup";
import { object, array, number } from "yup";
import { useForm } from "vee-validate";
import { getUserList } from "@/api/modules/user";
import { WarrantyTask, urgencyDegreeOptions } from "@/api/modules/warrantyTask";

interface DialogProps {
  row: WarrantyTask.TaskRes;
  api: (params: any) => Promise<any>;
  getTableList: () => void;
}

const dialogVisible = ref(false);
const dialogProps = ref<DialogProps | undefined>();

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    executorAccountIdList: array().required().label("执行人员"),
    urgencyDegreeType: number().required().label("紧急程度")
  })
);

const { defineComponentBinds, handleSubmit, errors, setValues } = useForm<{
  executorAccountIdList: string[];
  urgencyDegreeType: number;
}>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  executorAccountIdList: defineComponentBinds("executorAccountIdList", elPlusConfig),
  urgencyDegreeType: defineComponentBinds("urgencyDegreeType", elPlusConfig)
});

/**
 * 人员列表
 */
const {
  state: executorList,
  execute: refreshExecutorList,
  isLoading: executorListLoading
} = useAsyncState(async (nickname: string) => {
  const { data } = await getUserList(
    {
      nickname,
      pageIndex: 1,
      pageSize: 30
    },
    false
  );
  return data.records;
}, []);

// 接收父组件参数
const acceptParams = (params: DialogProps) => {
  dialogProps.value = params;
  dialogVisible.value = true;
  const executorAccountIdList = params.row.executorAccountList?.map(item => item.id.toString()) ?? [];
  const urgencyDegreeType = params.row.urgencyDegreeType ?? undefined;
  let shouldValidate = false;
  if (executorAccountIdList.length > 0 || urgencyDegreeType !== undefined) {
    shouldValidate = true;
  }

  setValues(
    {
      executorAccountIdList,
      urgencyDegreeType
    },
    shouldValidate
  );
};

const onSubmit = handleSubmit(async values => {
  if (dialogProps.value) {
    await dialogProps.value.api({
      id: dialogProps.value.row.id,
      executorAccountIdList: values.executorAccountIdList,
      urgencyDegreeType: values.urgencyDegreeType
    });
    dialogVisible.value = false;
    await dialogProps.value?.getTableList?.();
  }
});

defineExpose({
  acceptParams
});
</script>
