// ? 系统全局字典

export enum Gender {
  /**
   * 未知
   */
  Unknown = -1,
  /**
   * 女
   */
  Female = 0,
  /**
   * 男
   */
  Male = 1
}

/**
 * @description：用户性别
 */
export const genderType = [
  { label: "男", value: Gender.Male },
  { label: "女", value: Gender.Female }
];

/**
 * 状态枚举
 */
export enum StatusEnum {
  OFF = 0,
  NO = 1
}

/**
 * @description：状态
 */
export const Status = [
  { label: "启用", value: StatusEnum.NO, tagType: "success" },
  { label: "禁用", value: StatusEnum.OFF, tagType: "danger" }
];

/**
 * 服务类型枚举
 */

export enum ServerType {
  /**
   * -1 : 未知
   */
  Unknown = -1,
  /**
   * 0 : 其他
   */
  Other = 0,
  /**
   * 1 : 住院陪护
   */
  HospitalCare = 1,
  /**
   * 2 : 养老院陪护
   */
  NursingHomeCare = 2,
  /**
   * 3 : 居家陪护
   */
  HomeCare = 3,
  /**
   * 4 : 家政陪护
   */
  DomesticCare = 4
}

/**
 * 服务类型
 */
export const serverTypeOptions = [
  // { value: ServerType.Unknown, label: "未知" },
  { value: ServerType.Other, label: "其他" },
  { value: ServerType.HospitalCare, label: "住院陪护" }
  // { value: ServerType.NursingHomeCare, label: "养老院陪护" },
  // { value: ServerType.HomeCare, label: "居家陪护" },
  // { value: ServerType.HousekeepingCare, label: "家政陪护" }
];
