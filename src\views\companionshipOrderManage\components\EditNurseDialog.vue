<template>
  <el-dialog title="请选择护工" v-model="dialogVisible">
    <el-form ref="ruleFormRef" label-width="100px" label-suffix=" :" @submit="onSubmit">
      <el-form-item :error="errors.nursingList" label="护工" required>
        <el-select
          class="w-full"
          v-bind="formData.nursingList"
          filterable
          remote
          multiple
          reserve-keyword
          :remote-method="remoteMethod"
          value-key="value"
          :loading="isLoading"
        >
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item
        :error="errors.presetTime"
        label="生效时间"
        required
        v-show="companionshipOrderStore.dialogInfo.info?.orderStatus === CompanionshipOrder.OrderStatus.Nursing"
      >
        <el-date-picker
          type="datetime"
          v-bind="formData.presetTime"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="defaultTime"
          format="YYYY-MM-DD HH 时"
          time-format="HH 时"
          value-format="x"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useCompanionshipOrderStore, DialogType } from "@/stores/modules/companionshipOrder";
import { updateNursing, CompanionshipOrder } from "@/api/modules/companionshipOrder";
import { toTypedSchema } from "@vee-validate/yup";
import { object, array, number } from "yup";
import { useForm } from "vee-validate";
import { Nurse, getNurseList } from "@/api/modules/nurseManage";
import dayjs from "dayjs";
const defaultTime = new Date(2000, 1, 1, 12, 0, 0);
const companionshipOrderStore = useCompanionshipOrderStore();

interface SelectOption {
  value: string;
  label: string;
}

const {
  state: options,
  isLoading,
  execute: refreshoptions
} = useAsyncState<SelectOption[], [string?]>(
  async query => {
    const { data } = await getNurseList({ name: query, pageIndex: 1, pageSize: 30, status: Nurse.NursingStatus.Active }, false);
    return data.records.map((item: any) => ({ value: item.id, label: item.name }));
  },
  [],
  { immediate: false }
);

let orderId: string;
let orderStatus: CompanionshipOrder.OrderStatus;

const remoteMethod = (query?: string) => {
  refreshoptions(300, query);
};

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    nursingList: array().required().label("护工"),
    presetTime: number().when("$other", ([], schema) =>
      orderStatus === CompanionshipOrder.OrderStatus.Nursing ? schema.required().label("生效时间") : schema.label("生效时间")
    )
  })
);

const { defineComponentBinds, handleSubmit, resetForm, errors, setValues } = useForm<{
  nursingList: Array<{ value: string; label: string }>;
  presetTime?: number;
}>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  nursingList: defineComponentBinds("nursingList", elPlusConfig),
  presetTime: defineComponentBinds("presetTime", elPlusConfig)
});

const DIALOGTYPE = DialogType.Nurse;
const dialogVisible = computed<boolean>({
  set(val) {
    if (val) {
      companionshipOrderStore.dialogInfo.dialogVisible = DIALOGTYPE;
    } else {
      companionshipOrderStore.dialogInfo.dialogVisible = DialogType.None;
    }
    resetForm();
  },
  get() {
    if (companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE) {
      refreshoptions();
      orderId = companionshipOrderStore.dialogInfo.info!.orderId;
      orderStatus = companionshipOrderStore.dialogInfo.info!.orderStatus;
      setValues({
        nursingList: companionshipOrderStore.dialogInfo.info!.nursingList?.map(({ id, name }: { id: string; name: string }) => ({
          value: id,
          label: name
        })),
        presetTime: companionshipOrderStore.dialogInfo.info!.time
      });
    }
    return companionshipOrderStore.dialogInfo.dialogVisible === DIALOGTYPE;
  }
});

const onSubmit = handleSubmit(async values => {
  const params = {
    orderId: orderId,
    nursingIdList: values.nursingList.map(item => item.value),
    presetTime: dayjs(values.presetTime).startOf("hour").valueOf()
  };
  if (orderStatus !== CompanionshipOrder.OrderStatus.Nursing) {
    params.presetTime = 0;
  }
  console.log(params);

  await updateNursing(params);
  ElMessage("修改成功");
  companionshipOrderStore.dialogInfo = {
    info: undefined,
    dialogVisible: DialogType.None
  };
  companionshipOrderStore.getTableList!();
});
</script>
